# MyBatis 核心面试题

## 目录
1. [MyBatis中#{}和${}的区别](#1-mybatis中和的区别)
2. [MyBatis执行DML语句的返回值](#2-mybatis执行dml语句的返回值)

---

## 1. MyBatis中#{}和${}的区别

### 问题：MyBatis中#与$的区别是什么？（面试题）

**答案：**
| 特性 | #{} | ${} |
|------|-----|-----|
| **预编译** | 支持，防SQL注入 | 不支持，有SQL注入风险 |
| **参数类型** | 任意类型 | 只能是字符串 |
| **使用场景** | 参数值 | 动态表名、列名 |
| **性能** | 更好（预编译） | 较差（每次编译） |
| **占位符** | 是占位符，会替换为? | 是字符串拼接符号，将参数值直接拼接到SQL中 |

**详细解释：**
- **#{}**：是占位符，会替换为?，生成预编译SQL（推荐）
- **${}**：是字符串拼接符号，将参数值直接拼接到SQL中

```sql
-- 安全的参数传递
SELECT * FROM user WHERE id = #{id}
-- 实际执行：SELECT * FROM user WHERE id = ?

-- 动态表名（注意安全性）
SELECT * FROM ${tableName} WHERE id = #{id}
-- 实际执行：SELECT * FROM user WHERE id = ?
```

---

## 2. MyBatis执行DML语句的返回值

### 问题：MyBatis中执行DML语句时，有没有返回值？

**答案：**
**有**，返回`int`类型，表示DML语句执行影响的记录数

```java
// 插入操作
@Insert("INSERT INTO dept (name, create_time) VALUES (#{name}, #{createTime})")
int insert(Dept dept);  // 返回插入的记录数

// 更新操作
@Update("UPDATE dept SET name = #{name} WHERE id = #{id}")
int update(Dept dept);  // 返回更新的记录数

// 删除操作
@Delete("DELETE FROM dept WHERE id = #{id}")
int delete(Integer id); // 返回删除的记录数

// 使用示例
public void saveDept(Dept dept) {
    int rows = deptMapper.insert(dept);
    if (rows > 0) {
        System.out.println("插入成功，影响行数：" + rows);
    } else {
        System.out.println("插入失败");
    }
}
```
