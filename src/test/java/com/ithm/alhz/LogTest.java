package com.ithm.alhz;

import org.junit.jupiter.api.Test;

public class LogTest {

    @Test
    public void testLog(){
        System.out.println("开始计算....");

        int sum = 0;

        try{
            int [] nums = {1,5,3,2,1,4,5,4,6,7,4,32,2,23};

            for(int i=0;i<nums.length;i++){
                sum+=nums[i];
            }

        }catch (Exception e){
            System.out.println("程序出错：" + e.getMessage());
        }

        System.out.println("计算结果：" + sum);
    }
}
