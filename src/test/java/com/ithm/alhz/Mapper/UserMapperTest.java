package com.ithm.alhz.Mapper;


import com.ithm.alhz.MybatisStartApplication;
import com.ithm.alhz.entity.User;
import com.ithm.alhz.mapper.UserMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

//作用：会加载spring环境（ioc容器）
@SpringBootTest(classes = MybatisStartApplication.class)
public class UserMapperTest {

    @Autowired
    private UserMapper userMapper;

    @Test
    public void testList(){
        List<User>  users = userMapper.list();

        for(User user:users){
            System.out.println(user);
        }
    }
}
