<!DOCTYPE html>
<html>
  <head>
    <title>学员信息统计</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/学员信息统计/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/学员信息统计/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (左侧菜单) -->

      <!-- Unnamed (矩形) -->
      <div id="u898" class="ax_default box_2">
        <div id="u898_div" class=""></div>
        <div id="u898_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 内容管理 (动态面板) -->
      <div id="u899" class="ax_default" data-label="内容管理">
        <div id="u899_state0" class="panel_state" data-label="展开" style="">
          <div id="u899_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u900" class="ax_default _默认样式">
              <div id="u900_div" class=""></div>
              <div id="u900_text" class="text ">
                <p style="font-size:14px;"><span style="font-size:16px;">&nbsp;○ 系统信息管理&nbsp; &nbsp; </span><span>&gt;&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u901" class="ax_default _默认样式 selected" selectiongroup="u897二级菜单">
              <div id="u901_div" class="selected"></div>
              <div id="u901_text" class="text ">
                <p><span>部门管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u902" class="ax_default _默认样式" selectiongroup="u897二级菜单">
              <div id="u902_div" class=""></div>
              <div id="u902_text" class="text ">
                <p><span>员工管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u903" class="ax_default _默认样式">
              <div id="u903_div" class=""></div>
              <div id="u903_text" class="text ">
                <p style="font-size:14px;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;font-size:16px;">○</span><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;font-size:16px;"> 班级学员管理&nbsp; &nbsp; </span><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;">&gt;&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u904" class="ax_default _默认样式 selected" selectiongroup="u897二级菜单">
              <div id="u904_div" class="selected"></div>
              <div id="u904_text" class="text ">
                <p><span>班级管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u905" class="ax_default _默认样式" selectiongroup="u897二级菜单">
              <div id="u905_div" class=""></div>
              <div id="u905_text" class="text ">
                <p><span>学员管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u906" class="ax_default _默认样式">
              <div id="u906_div" class=""></div>
              <div id="u906_text" class="text ">
                <p style="font-size:14px;"><span style="font-size:16px;">&nbsp;○ 数据统计管理&nbsp; &nbsp; </span><span>&gt;&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u907" class="ax_default _默认样式 selected" selectiongroup="u897二级菜单">
              <div id="u907_div" class="selected"></div>
              <div id="u907_text" class="text ">
                <p><span>员工信息统计</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u908" class="ax_default _默认样式" selectiongroup="u897二级菜单">
              <div id="u908_div" class=""></div>
              <div id="u908_text" class="text ">
                <p><span>学员信息统计</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u909" class="ax_default _默认样式">
              <div id="u909_div" class=""></div>
              <div id="u909_text" class="text ">
                <p><span style="font-family:'Arial Normal', 'Arial';font-weight:400;">○</span><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;"> 首页</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u899_state1" class="panel_state" data-label="收起" style="visibility: hidden;">
          <div id="u899_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u910" class="ax_default box_1">
              <div id="u910_div" class=""></div>
              <div id="u910_text" class="text ">
                <p style="font-size:14px;"><span style="font-family:'FontAwesome';font-weight:400;font-style:normal;font-size:16px;"> </span><span style="font-family:'微软雅黑';font-weight:400;">内容管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u911" class="ax_default _文本">
              <div id="u911_div" class=""></div>
              <div id="u911_text" class="text ">
                <p><span></span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 顶部菜单 (动态面板) -->
      <div id="u912" class="ax_default" data-label="顶部菜单">
        <div id="u912_state0" class="panel_state" data-label="框架" style="">
          <div id="u912_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u913" class="ax_default _默认样式">
              <div id="u913_div" class=""></div>
              <div id="u913_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u914" class="ax_default box_2">
              <div id="u914_div" class=""></div>
              <div id="u914_text" class="text ">
                <p><span style="font-family:'Consolas Bold', 'Consolas Regular', 'Consolas';font-weight:700;color:#FFFFFF;">tlias</span><span style="font-family:'华文彩云';font-weight:400;"> </span><span style="font-family:'楷体 Bold', '楷体';font-weight:700;color:#FFFFFF;">智能学习辅助系统</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u915" class="ax_default image">
        <img id="u915_img" class="img " src="images/首页/u18.png"/>
        <div id="u915_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u916" class="ax_default box_1">
        <div id="u916_div" class=""></div>
        <div id="u916_text" class="text ">
          <p><span>&nbsp;退出登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u917" class="ax_default box_1">
        <div id="u917_div" class=""></div>
        <div id="u917_text" class="text ">
          <p><span>&nbsp; 修改密码</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u918" class="ax_default image">
        <img id="u918_img" class="img " src="images/首页/u21.png"/>
        <div id="u918_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u919" class="ax_default image">
        <img id="u919_img" class="img " src="images/首页/u22.png"/>
        <div id="u919_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u920" class="ax_default image">
        <img id="u920_img" class="img " src="images/首页/u23.png"/>
        <div id="u920_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u897" style="display:none; visibility:hidden;"></div>

      <!-- degreeChart (矩形) -->
      <div id="u921" class="ax_default _形状" data-label="degreeChart">
        <img id="u921_img" class="img " src="images/学员信息统计/degreechart_u921.svg"/>
        <div id="u921_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u922" class="ax_default _文本">
        <div id="u922_div" class=""></div>
        <div id="u922_text" class="text ">
          <p><span>学员学历统计</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u923" class="ax_default _文本">
        <div id="u923_div" class=""></div>
        <div id="u923_text" class="text ">
          <p><span>班级人数统计</span></p>
        </div>
      </div>

      <!-- countChart (矩形) -->
      <div id="u924" class="ax_default _形状" data-label="countChart">
        <img id="u924_img" class="img " src="images/学员信息统计/countchart_u924.svg"/>
        <div id="u924_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 对话框 (动态面板) -->
      <div id="u925" class="ax_default ax_default_hidden" data-label="对话框" style="display:none; visibility: hidden">
        <div id="u925_state0" class="panel_state" data-label="退出确认" style="">
          <div id="u925_state0_content" class="panel_state_content">
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u926" class="ax_default _文本">
        <div id="u926_div" class=""></div>
        <div id="u926_text" class="text ">
          <p style="font-size:28px;text-align:center;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;">学员信息统计-需求说明</span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:28px;text-align:left;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;"><br></span></p><p style="font-size:28px;text-align:left;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;">页面开发规则</span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;">1. 统计学员的学历比例 , 以饼状图形式展示 。</span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;">2. 统计每个班级的学生的数量，以柱状图展示</span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
