$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,d,p,f),q,_(r,[_(s,t,u,v,w,x,y,z),_(s,A,u,B,w,x,y,C,D,[_(s,E,u,F,w,x,y,G),_(s,H,u,I,w,x,y,J)]),_(s,K,u,L,w,x,y,M,D,[_(s,N,u,O,w,x,y,P),_(s,Q,u,R,w,x,y,S)]),_(s,T,u,U,w,x,y,V,D,[_(s,W,u,X,w,x,y,Y),_(s,Z,u,ba,w,x,y,bb)]),_(s,bc,u,bd,w,x,y,be),_(s,bf,u,bg,w,x,y,bh)]),bi,[bj,bk,bl],bm,[bn,bo,bp],bq,_(br,bs),bt,_(bu,_(s,bv,bw,bx,by,bz,bA,bB,bC,bD,bE,_(bF,bG,bH,bI,bJ,bK),bL,bM,bN,f,bO,bP,bQ,bB,bR,bB,bS,bT,bU,f,bV,_(bW,bX,bY,bX),bZ,_(ca,bX,cb,bX),cc,d,cd,f,ce,bv,cf,_(bF,bG,bH,cg),ch,_(bF,bG,bH,ci),cj,ck,cl,bG,bJ,ck,cm,cn,co,cp,cq,cr,cs,cr,ct,cr,cu,cr,cv,_(),cw,null,cx,null,cy,cn,cz,_(cA,f,cB,cC,cD,cC,cE,cC,bH,_(cF,cG,cH,cG,cI,cG,cJ,cK)),cL,_(cA,f,cB,bX,cD,cC,cE,cC,bH,_(cF,cG,cH,cG,cI,cG,cJ,cK)),cM,_(cA,f,cB,bK,cD,bK,cE,cC,bH,_(cF,cG,cH,cG,cI,cG,cJ,cN)),cO,cP),cQ,_(cR,_(s,cS),cT,_(s,cU,bL,cV,by,cW,cj,cn,cf,_(bF,bG,bH,cX),bO,cY,co,cZ,cq,cn,cs,cn,ct,cn,cu,cn),da,_(s,db,bL,dc,by,cW,cj,cn,cf,_(bF,bG,bH,cX),bO,cY,co,cZ,cq,cn,cs,cn,ct,cn,cu,cn),dd,_(s,de,bL,df,by,cW,cj,cn,cf,_(bF,bG,bH,cX),bO,cY,co,cZ,cq,cn,cs,cn,ct,cn,cu,cn),dg,_(s,dh,bL,di,by,cW,cj,cn,cf,_(bF,bG,bH,cX),bO,cY,co,cZ,cq,cn,cs,cn,ct,cn,cu,cn),dj,_(s,dk,by,cW,cj,cn,cf,_(bF,bG,bH,cX),bO,cY,co,cZ,cq,cn,cs,cn,ct,cn,cu,cn),dl,_(s,dm,bL,dn,by,cW,cj,cn,cf,_(bF,bG,bH,cX),bO,cY,co,cZ,cq,cn,cs,cn,ct,cn,cu,cn),dp,_(s,dq,cj,cn,cf,_(bF,bG,bH,cX),bO,cY,co,cZ,cq,cn,cs,cn,ct,cn,cu,cn),dr,_(s,ds,cj,bD,cf,_(bF,bG,bH,cX)),dt,_(s,du,bE,_(bF,bG,bH,dv,bJ,bK)),dw,_(s,dx,cf,_(bF,bG,bH,dy)),dz,_(s,dA,cf,_(bF,dB,dC,_(bW,dD,bY,bX),dE,_(bW,dD,bY,bK),dF,[_(bH,cg,dG,bX),_(bH,dH,dG,bX),_(bH,dI,dG,bK),_(bH,cg,dG,bK)])),dJ,_(s,dK,ch,_(bF,bG,bH,dL)),dM,_(s,dN,ch,_(bF,bG,bH,dO),cj,cr),dP,_(s,dQ),dR,_(s,dS,cf,_(bF,bG,bH,dH),cj,cn),cw,_(s,dT,cj,cn),dU,_(s,dV,cf,_(bF,bG,bH,dH)),dW,_(s,dX),dY,_(s,dZ,bE,_(bF,bG,bH,dL,bJ,bK),bO,cY,co,cp),ea,_(s,eb,bE,_(bF,bG,bH,dL,bJ,bK),bO,cY,co,cZ),ec,_(s,ed,bE,_(bF,bG,bH,dL,bJ,bK),bO,cY,co,cZ),ee,_(s,ef,bO,cY,co,cZ),eg,_(s,eh),ei,_(s,ej,bE,_(bF,bG,bH,dv,bJ,bK)),ek,_(s,el,cf,_(bF,bG,bH,dy))),em,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="useLabels",o="useViews",p="loadFeedbackPlugin",q="sitemap",r="rootNodes",s="id",t="zhll7a",u="pageName",v="首页",w="type",x="Wireframe",y="url",z="首页.html",A="90c0hw",B="班级学员管理",C="班级学员管理.html",D="children",E="r9zvln",F="班级管理",G="班级管理.html",H="kj5myz",I="学员管理",J="学员管理.html",K="7zbdsq",L="系统信息管理",M="系统信息管理.html",N="9z1wok",O="部门管理",P="部门管理.html",Q="fml41q",R="员工管理",S="员工管理.html",T="x1823z",U="数据统计管理",V="数据统计管理.html",W="7hx0g7",X="员工信息统计",Y="员工信息统计.html",Z="1q80w1",ba="学员信息统计",bb="学员信息统计.html",bc="rg25cj",bd="登录",be="登录.html",bf="n9ux8c",bg="修改密码",bh="修改密码.html",bi="additionalJs",bj="plugins/sitemap/sitemap.js",bk="plugins/page_notes/page_notes.js",bl="plugins/debug/debug.js",bm="additionalCss",bn="plugins/sitemap/styles/sitemap.css",bo="plugins/page_notes/styles/page_notes.css",bp="plugins/debug/styles/debug.css",bq="globalVariables",br="onloadvariable",bs="",bt="stylesheet",bu="defaultStyle",bv="627587b6038d43cca051c114ac41ad32",bw="fontName",bx="'Arial Normal', 'Arial'",by="fontWeight",bz="400",bA="fontStyle",bB="normal",bC="fontStretch",bD="5",bE="foreGroundFill",bF="fillType",bG="solid",bH="color",bI=0xFF333333,bJ="opacity",bK=1,bL="fontSize",bM="13px",bN="underline",bO="horizontalAlignment",bP="center",bQ="lineSpacing",bR="characterSpacing",bS="letterCase",bT="none",bU="strikethrough",bV="location",bW="x",bX=0,bY="y",bZ="size",ca="width",cb="height",cc="visible",cd="limbo",ce="baseStyle",cf="fill",cg=0xFFFFFFFF,ch="borderFill",ci=0xFF797979,cj="borderWidth",ck="1",cl="linePattern",cm="cornerRadius",cn="0",co="verticalAlignment",cp="middle",cq="paddingLeft",cr="2",cs="paddingTop",ct="paddingRight",cu="paddingBottom",cv="stateStyles",cw="image",cx="imageFilter",cy="rotation",cz="outerShadow",cA="on",cB="offsetX",cC=5,cD="offsetY",cE="blurRadius",cF="r",cG=0,cH="g",cI="b",cJ="a",cK=0.349019607843137,cL="innerShadow",cM="textShadow",cN=0.647058823529412,cO="viewOverride",cP="19e82109f102476f933582835c373474",cQ="customStyles",cR="_形状",cS="40519e9ec4264601bfb12c514e4f4867",cT="_标题_1",cU="********************************",cV="32px",cW="bold",cX=0xFFFFFF,cY="left",cZ="top",da="_标题_2",db="b3a15c9ddde04520be40f94c8168891e",dc="24px",dd="_标题_3",de="8c7a4c5ad69a4369a5f7788171ac0b32",df="18px",dg="_标题_4",dh="e995c891077945c89c0b5fe110d15a0b",di="14px",dj="_标题_5",dk="386b19ef4be143bd9b6c392ded969f89",dl="_标题_6",dm="fc3b9a13b5574fa098ef0a1db9aac861",dn="10px",dp="_文本",dq="4988d43d80b44008a4a415096f1632af",dr="arrow",ds="d148f2c5268542409e72dde43e40043e",dt="_表单提示",du="4889d666e8ad4c5e81e59863039a5cc0",dv=0xFF999999,dw="_表单禁用",dx="9bd0236217a94d89b0314c8c7fc75f16",dy=0xFFF0F0F0,dz="_流程形状",dA="df01900e3c4e43f284bafec04b0864c4",dB="linearGradient",dC="startPoint",dD=0.5,dE="endPoint",dF="stops",dG="offset",dH=0xFFF2F2F2,dI=0xFFE4E4E4,dJ="_直线___行距",dK="804e3bae9fce4087aeede56c15b6e773",dL=0xFF000000,dM="_连接符",dN="699a012e142a4bcba964d96e88b88bdf",dO=0xFF0099CC,dP="box_1",dQ="********************************",dR="box_2",dS="********************************",dT="75a91ee5b9d042cfa01b8d565fe289c0",dU="placeholder",dV="c50e74f669b24b37bd9c18da7326bccd",dW="_默认样式",dX="ee0d561981f0497aa5993eaf71a3de39",dY="text_field",dZ="ac6e148b6d2a4ceeaf4ccdf56dd34b88",ea="text_area",eb="42ee17691d13435b8256d8d0a814778f",ec="droplist",ed="e27dca23909f4f9593afbedf12f21e7a",ee="checkbox",ef="********************************",eg="table_cell",eh="83b0eac9d47f4356b742258060022575",ei="form_hint",ej="3c35f7f584574732b5edbd0cff195f77",ek="form_disabled",el="2829faada5f8449da03773b96e566862",em="duplicateStyles";
return _creator();
})());