<!DOCTYPE html>
<html>
  <head>
    <title>首页</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/首页/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/首页/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (左侧菜单) -->

      <!-- Unnamed (矩形) -->
      <div id="u1" class="ax_default box_2">
        <div id="u1_div" class=""></div>
        <div id="u1_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 内容管理 (动态面板) -->
      <div id="u2" class="ax_default" data-label="内容管理">
        <div id="u2_state0" class="panel_state" data-label="展开" style="">
          <div id="u2_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u3" class="ax_default _默认样式">
              <div id="u3_div" class=""></div>
              <div id="u3_text" class="text ">
                <p style="font-size:14px;"><span style="font-size:16px;">&nbsp;○ 系统信息管理&nbsp; &nbsp; </span><span>&gt;&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u4" class="ax_default _默认样式 selected" selectiongroup="u0二级菜单">
              <div id="u4_div" class="selected"></div>
              <div id="u4_text" class="text ">
                <p><span>部门管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5" class="ax_default _默认样式" selectiongroup="u0二级菜单">
              <div id="u5_div" class=""></div>
              <div id="u5_text" class="text ">
                <p><span>员工管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u6" class="ax_default _默认样式">
              <div id="u6_div" class=""></div>
              <div id="u6_text" class="text ">
                <p style="font-size:14px;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;font-size:16px;">○</span><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;font-size:16px;"> 班级学员管理&nbsp; &nbsp; </span><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;">&gt;&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u7" class="ax_default _默认样式 selected" selectiongroup="u0二级菜单">
              <div id="u7_div" class="selected"></div>
              <div id="u7_text" class="text ">
                <p><span>班级管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8" class="ax_default _默认样式" selectiongroup="u0二级菜单">
              <div id="u8_div" class=""></div>
              <div id="u8_text" class="text ">
                <p><span>学员管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u9" class="ax_default _默认样式">
              <div id="u9_div" class=""></div>
              <div id="u9_text" class="text ">
                <p style="font-size:14px;"><span style="font-size:16px;">&nbsp;○ 数据统计管理&nbsp; &nbsp; </span><span>&gt;&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u10" class="ax_default _默认样式 selected" selectiongroup="u0二级菜单">
              <div id="u10_div" class="selected"></div>
              <div id="u10_text" class="text ">
                <p><span>员工信息统计</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u11" class="ax_default _默认样式" selectiongroup="u0二级菜单">
              <div id="u11_div" class=""></div>
              <div id="u11_text" class="text ">
                <p><span>学员信息统计</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u12" class="ax_default _默认样式">
              <div id="u12_div" class=""></div>
              <div id="u12_text" class="text ">
                <p><span style="font-family:'Arial Normal', 'Arial';font-weight:400;">○</span><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;"> 首页</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2_state1" class="panel_state" data-label="收起" style="visibility: hidden;">
          <div id="u2_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u13" class="ax_default box_1">
              <div id="u13_div" class=""></div>
              <div id="u13_text" class="text ">
                <p style="font-size:14px;"><span style="font-family:'FontAwesome';font-weight:400;font-style:normal;font-size:16px;"> </span><span style="font-family:'微软雅黑';font-weight:400;">内容管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u14" class="ax_default _文本">
              <div id="u14_div" class=""></div>
              <div id="u14_text" class="text ">
                <p><span></span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 顶部菜单 (动态面板) -->
      <div id="u15" class="ax_default" data-label="顶部菜单">
        <div id="u15_state0" class="panel_state" data-label="框架" style="">
          <div id="u15_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u16" class="ax_default _默认样式">
              <div id="u16_div" class=""></div>
              <div id="u16_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u17" class="ax_default box_2">
              <div id="u17_div" class=""></div>
              <div id="u17_text" class="text ">
                <p><span style="font-family:'Consolas Bold', 'Consolas Regular', 'Consolas';font-weight:700;color:#FFFFFF;">tlias</span><span style="font-family:'华文彩云';font-weight:400;"> </span><span style="font-family:'楷体 Bold', '楷体';font-weight:700;color:#FFFFFF;">智能学习辅助系统</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u18" class="ax_default image">
        <img id="u18_img" class="img " src="images/首页/u18.png"/>
        <div id="u18_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u19" class="ax_default box_1">
        <div id="u19_div" class=""></div>
        <div id="u19_text" class="text ">
          <p><span>&nbsp;退出登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u20" class="ax_default box_1">
        <div id="u20_div" class=""></div>
        <div id="u20_text" class="text ">
          <p><span>&nbsp; 修改密码</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u21" class="ax_default image">
        <img id="u21_img" class="img " src="images/首页/u21.png"/>
        <div id="u21_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u22" class="ax_default image">
        <img id="u22_img" class="img " src="images/首页/u22.png"/>
        <div id="u22_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u23" class="ax_default image">
        <img id="u23_img" class="img " src="images/首页/u23.png"/>
        <div id="u23_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u0" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u24" class="ax_default _形状">
        <img id="u24_img" class="img " src="images/首页/u24.svg"/>
        <div id="u24_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u25" class="ax_default image">
        <img id="u25_img" class="img " src="images/首页/u25.png"/>
        <div id="u25_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
