$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn)),i,_(j,k,l,k)),bo,_(),bp,_(bq,_(br,bs,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,bA,br,bB,bC,bD,bE,_(bF,_(h,bG)),bH,_(bI,bJ,bK,_(bL,bM,bN,bF,bO,_(),bP,[]),bQ,bd),bR,bS),_(bz,bA,br,bT,bC,bD,bE,_(bU,_(h,bV)),bH,_(bI,bJ,bK,_(bL,bM,bN,bU,bO,_(),bP,[]),bQ,bd),bR,bS)])])),bW,_(bX,[_(bY,bZ,ca,h,cb,cc,u,cd,ce,cd,cf,cg,z,_(i,_(j,ch,l,ci)),bo,_(),cj,_(),ck,cl),_(bY,cm,ca,cn,cb,co,u,cp,ce,cp,cf,cg,z,_(A,cq,i,_(j,cr,l,cs),ct,_(cu,cv,cw,cx),X,_(F,G,H,cy)),bo,_(),cj,_(),cz,_(cA,cB),cC,bd),_(bY,cD,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(A,cE,i,_(j,cF,l,cG),ct,_(cu,cH,cw,cI),cJ,cK),bo,_(),cj,_(),cC,bd),_(bY,cL,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(A,cE,i,_(j,cM,l,cG),ct,_(cu,cN,cw,cI),cJ,cK),bo,_(),cj,_(),cC,bd),_(bY,cO,ca,cP,cb,co,u,cp,ce,cp,cf,cg,z,_(A,cq,i,_(j,cQ,l,cs),ct,_(cu,cR,cw,cx),X,_(F,G,H,cy)),bo,_(),cj,_(),cz,_(cA,cS),cC,bd),_(bY,cT,ca,cU,cb,cV,u,cW,ce,cW,cf,bd,z,_(i,_(j,cX,l,ci),ct,_(cu,cY,cw,k),cf,bd),bo,_(),cj,_(),cZ,D,da,k,db,dc,dd,k,de,cg,df,dg,dh,bd,di,bd,dj,[_(bY,dk,ca,dl,u,dm,bX,[],z,_(E,_(F,G,H,dn),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_())]),_(bY,dp,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(A,cE,i,_(j,dq,l,dr),ct,_(cu,ds,cw,dt)),bo,_(),cj,_(),cC,bd)])),du,_(dv,_(s,dv,u,dw,g,cc,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn)),i,_(j,k,l,k)),m,[],bp,_(),bW,_(bX,[_(bY,dx,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(i,_(j,dy,l,ci),A,dz,E,_(F,G,H,dA)),bo,_(),cj,_(),cC,bd),_(bY,dB,ca,dC,cb,cV,u,cW,ce,cW,cf,cg,z,_(i,_(j,dy,l,dD),ct,_(cu,dE,cw,dF)),bo,_(),cj,_(),bp,_(dG,_(br,dH,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dI,br,dJ,bC,dK,bE,_(dL,_(h,dJ)),bR,dM,bH,_(bI,r,b,dN,bQ,cg))])])),df,dO,dh,cg,di,bd,dj,[_(bY,dP,ca,dQ,u,dm,bX,[_(bY,dR,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,z,_(dU,dV,dW,_(F,G,H,dX,dY,dZ),i,_(j,dy,l,ea),A,eb,X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,en,E,_(F,G,H,eo),ct,_(cu,k,cw,ep)),bo,_(),cj,_(),cC,bd),_(bY,eq,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,eg,cg,z,_(T,er,dU,es,i,_(j,dy,l,ea),A,eb,ct,_(cu,k,cw,et),X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_(dW,_(F,G,H,ef,dY,dZ),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,eu,E,_(F,G,H,ev),cJ,ew),bo,_(),cj,_(),bp,_(ex,_(br,ey,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dI,br,ez,bC,dK,bE,_(eA,_(h,ez)),bR,dM,bH,_(bI,r,b,eB,bQ,cg))])])),eC,cg,cC,bd),_(bY,eD,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,z,_(T,er,dU,es,i,_(j,dy,l,ea),A,eb,ct,_(cu,k,cw,eE),X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_(dW,_(F,G,H,ef,dY,dZ),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,eu,E,_(F,G,H,ev),cJ,ew),bo,_(),cj,_(),bp,_(ex,_(br,ey,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dI,br,eF,bC,dK,bE,_(eG,_(h,eF)),bR,dM,bH,_(bI,r,b,eH,bQ,cg))])])),eC,cg,cC,bd),_(bY,eI,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,z,_(dW,_(F,G,H,dX,dY,dZ),i,_(j,dy,l,ea),A,eb,X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,en,E,_(F,G,H,eo),ct,_(cu,k,cw,eJ)),bo,_(),cj,_(),cC,bd),_(bY,eK,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,eg,cg,z,_(T,er,dU,es,i,_(j,dy,l,ea),A,eb,ct,_(cu,k,cw,eL),X,_(F,G,H,ec),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,eu,E,_(F,G,H,ev),cJ,ew),bo,_(),cj,_(),bp,_(ex,_(br,ey,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dI,br,dJ,bC,dK,bE,_(dL,_(h,dJ)),bR,dM,bH,_(bI,r,b,dN,bQ,cg))])])),eC,cg,cC,bd),_(bY,eM,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,z,_(T,er,dU,es,i,_(j,dy,l,ea),A,eb,ct,_(cu,k,cw,eN),X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_(dW,_(F,G,H,ef,dY,dZ),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,eu,E,_(F,G,H,ev),cJ,ew),bo,_(),cj,_(),bp,_(ex,_(br,ey,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dI,br,eO,bC,dK,bE,_(eP,_(h,eO)),bR,dM,bH,_(bI,r,b,eQ,bQ,cg))])])),eC,cg,cC,bd),_(bY,eR,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,z,_(dU,dV,dW,_(F,G,H,dX,dY,dZ),i,_(j,dy,l,ea),A,eb,X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,en,E,_(F,G,H,eo),ct,_(cu,k,cw,eS)),bo,_(),cj,_(),cC,bd),_(bY,eT,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,eg,cg,z,_(T,er,dU,es,i,_(j,dy,l,ea),A,eb,ct,_(cu,k,cw,eU),X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_(dW,_(F,G,H,ef,dY,dZ),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,eu,E,_(F,G,H,ev),cJ,ew),bo,_(),cj,_(),bp,_(ex,_(br,ey,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dI,br,eV,bC,dK,bE,_(eW,_(h,eV)),bR,dM,bH,_(bI,r,b,eX,bQ,cg))])])),eC,cg,cC,bd),_(bY,eY,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,z,_(T,er,dU,es,i,_(j,dy,l,ea),A,eb,ct,_(cu,k,cw,eZ),X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_(dW,_(F,G,H,ef,dY,dZ),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,eu,E,_(F,G,H,ev),cJ,ew),bo,_(),cj,_(),bp,_(ex,_(br,ey,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dI,br,fa,bC,dK,bE,_(fb,_(h,fa)),bR,dM,bH,_(bI,r,b,c,bQ,cg))])])),eC,cg,cC,bd),_(bY,fc,ca,h,cb,co,dS,dB,dT,bj,u,cp,ce,cp,cf,cg,z,_(dW,_(F,G,H,dX,dY,dZ),i,_(j,dy,l,ea),A,eb,X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,en,cJ,ew,E,_(F,G,H,eo),ct,_(cu,k,cw,fd)),bo,_(),cj,_(),bp,_(ex,_(br,ey,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,bA,br,fe,bC,bD,bE,_(ff,_(h,fe)),bH,_(bI,r,b,fg,bQ,cg),bR,bS)])])),eC,cg,cC,bd)],z,_(E,_(F,G,H,dn),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_()),_(bY,fh,ca,fi,u,dm,bX,[_(bY,fj,ca,h,cb,co,dS,dB,dT,fk,u,cp,ce,cp,cf,cg,z,_(dW,_(F,G,H,fl,dY,dZ),i,_(j,dy,l,ea),A,fm,X,_(F,G,H,ec),ed,_(ee,_(dW,_(F,G,H,ef,dY,dZ)),eg,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,eh,bk,ei,bl,ej,bm,dZ)),ek,el,em,en,E,_(F,G,H,fn)),bo,_(),cj,_(),cC,bd),_(bY,fo,ca,h,cb,co,dS,dB,dT,fk,u,cp,ce,cp,cf,cg,z,_(T,fp,dU,es,dW,_(F,G,H,fq,dY,dZ),ct,_(cu,fr,cw,k),i,_(j,fs,l,ea),A,cE,ek,D,ft,dc,fu,fv),bo,_(),cj,_(),cC,bd)],z,_(E,_(F,G,H,dn),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_())]),_(bY,fw,ca,fx,cb,cV,u,cW,ce,cW,cf,cg,z,_(i,_(j,ch,l,fy),ct,_(cu,k,cw,dZ)),bo,_(),cj,_(),df,dO,dh,bd,di,bd,dj,[_(bY,fz,ca,fA,u,dm,bX,[_(bY,fB,ca,h,cb,co,dS,fw,dT,bj,u,cp,ce,cp,cf,cg,z,_(dW,_(F,G,H,I,dY,dZ),i,_(j,ch,l,fC),A,eb,cJ,fD,fu,fD,ed,_(ee,_()),X,_(F,G,H,fE),Z,Q,V,Q,E,_(F,G,H,fF)),bo,_(),cj,_(),cC,bd),_(bY,fG,ca,h,cb,co,dS,fw,dT,bj,u,cp,ce,cp,cf,cg,z,_(i,_(j,fH,l,ea),A,dz,ct,_(cu,fI,cw,fJ),E,_(F,G,H,dn),ek,el,cJ,cK),bo,_(),cj,_(),cC,bd)],z,_(E,_(F,G,H,fK),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_())]),_(bY,fL,ca,h,cb,fM,u,fN,ce,fN,cf,cg,z,_(A,fO,i,_(j,fP,l,cG),ct,_(cu,dE,cw,fQ),J,null),bo,_(),cj,_(),cz,_(fR,fS)),_(bY,fT,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(dW,_(F,G,H,I,dY,dZ),i,_(j,fU,l,fy),A,fm,ct,_(cu,fV,cw,dZ),cJ,fv,E,_(F,G,H,dn),fW,Q),bo,_(),cj,_(),bp,_(ex,_(br,ey,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dI,br,fX,bC,dK,bE,_(fY,_(h,fX)),bR,dM,bH,_(bI,r,b,fZ,bQ,cg))])])),eC,cg,cC,bd),_(bY,ga,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(dW,_(F,G,H,I,dY,dZ),i,_(j,gb,l,fy),A,fm,ct,_(cu,gc,cw,dZ),cJ,fv,E,_(F,G,H,dn),fW,Q),bo,_(),cj,_(),bp,_(ex,_(br,ey,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,bA,br,gd,bC,bD,bE,_(ge,_(h,gd)),bH,_(bI,r,b,gf,bQ,cg),bR,bS)])])),eC,cg,cC,bd),_(bY,gg,ca,h,cb,fM,u,fN,ce,fN,cf,cg,z,_(A,fO,i,_(j,gh,l,gh),ct,_(cu,gi,cw,gj),J,null),bo,_(),cj,_(),cz,_(gk,gl)),_(bY,gm,ca,h,cb,fM,u,fN,ce,fN,cf,cg,z,_(A,fO,i,_(j,gn,l,gn),ct,_(cu,go,cw,gp),J,null),bo,_(),cj,_(),cz,_(gq,gr)),_(bY,gs,ca,h,cb,fM,u,fN,ce,fN,cf,cg,z,_(A,fO,i,_(j,gt,l,gu),ct,_(cu,gv,cw,gw),J,null),bo,_(),cj,_(),cz,_(gx,gy))]))),gz,_(gA,_(gB,gC,gD,_(gB,gE),gF,_(gB,gG),gH,_(gB,gI),gJ,_(gB,gK),gL,_(gB,gM),gN,_(gB,gO),gP,_(gB,gQ),gR,_(gB,gS),gT,_(gB,gU),gV,_(gB,gW),gX,_(gB,gY),gZ,_(gB,ha),hb,_(gB,hc),hd,_(gB,he),hf,_(gB,hg),hh,_(gB,hi),hj,_(gB,hk),hl,_(gB,hm),hn,_(gB,ho),hp,_(gB,hq),hr,_(gB,hs),ht,_(gB,hu),hv,_(gB,hw)),hx,_(gB,hy),hz,_(gB,hA),hB,_(gB,hC),hD,_(gB,hE),hF,_(gB,hG),hH,_(gB,hI)));}; 
var b="url",c="学员信息统计.html",d="generationDate",e=new Date(1700034921876.52),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="7be406f2baa04c5c8134416deaec548e",u="type",v="Axure:Page",w="学员信息统计",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="borderFill",Y=0xFF797979,Z="cornerRadius",ba="cornerVisibility",bb="outerShadow",bc="on",bd=false,be="offsetX",bf=5,bg="offsetY",bh="blurRadius",bi="r",bj=0,bk="g",bl="b",bm="a",bn=0.349019607843137,bo="adaptiveStyles",bp="interactionMap",bq="onLoad",br="description",bs="页面载入时",bt="cases",bu="conditionString",bv="isNewIfGroup",bw="caseColorHex",bx="9D33FA",by="actions",bz="action",bA="linkWindow",bB="在 当前窗口 打开 javascript:<br>var script = document.createElement('script');<br>script.type = &quot;text/javascript&quot;;<br>script.src =&quot;https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js&quot;;<br>document.head.appendChild(script);<br>setTimeout(function(){var dom =$('[data-label=degreeChart]').get(0);<br>var myChart = echarts.init(dom);<br>option = {<br>&nbsp; tooltip: {<br>&nbsp; &nbsp; trigger: 'item'<br>&nbsp; },<br>&nbsp; legend: {<br>&nbsp; &nbsp; top: '5%',<br>&nbsp; &nbsp; left: 'center'<br>&nbsp; },<br>&nbsp; series: [<br>&nbsp; &nbsp; {<br>&nbsp; &nbsp; &nbsp; name: '学员学历比例',<br>&nbsp; &nbsp; &nbsp; type: 'pie',<br>&nbsp; &nbsp; &nbsp; radius: ['40%', '70%'],<br>&nbsp; &nbsp; &nbsp; avoidLabelOverlap: false,<br>&nbsp; &nbsp; &nbsp; itemStyle: {<br>&nbsp; &nbsp; &nbsp; &nbsp; borderRadius: 10,<br>&nbsp; &nbsp; &nbsp; &nbsp; borderColor: '#fff',<br>&nbsp; &nbsp; &nbsp; &nbsp; borderWidth: 2<br>&nbsp; &nbsp; &nbsp; },<br>&nbsp; &nbsp; &nbsp; label: {<br>&nbsp; &nbsp; &nbsp; &nbsp; show: false,<br>&nbsp; &nbsp; &nbsp; &nbsp; position: 'center'<br>&nbsp; &nbsp; &nbsp; },<br>&nbsp; &nbsp; &nbsp; emphasis: {<br>&nbsp; &nbsp; &nbsp; &nbsp; label: {<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; show: true,<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; fontSize: 40,<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; fontWeight: 'bold'<br>&nbsp; &nbsp; &nbsp; &nbsp; }<br>&nbsp; &nbsp; &nbsp; },<br>&nbsp; &nbsp; &nbsp; labelLine: {<br>&nbsp; &nbsp; &nbsp; &nbsp; show: false<br>&nbsp; &nbsp; &nbsp; },<br>&nbsp; &nbsp; &nbsp; data: [<br>&nbsp; &nbsp; &nbsp; &nbsp; { value: 128, name: '初中' },<br>&nbsp; &nbsp; &nbsp; &nbsp; { value: 75, name: '高中' },<br>&nbsp; &nbsp; &nbsp; &nbsp; { value: 75, name: '大专' },<br>&nbsp; &nbsp; &nbsp; &nbsp; { value: 75, name: '本科' },<br>&nbsp; &nbsp; &nbsp; &nbsp; { value: 75, name: '硕士' },<br>&nbsp; &nbsp; &nbsp; &nbsp; { value: 75, name: '博士' }<br>&nbsp; &nbsp; &nbsp; ]<br>&nbsp; &nbsp; }<br>&nbsp; ]<br>};<br>if (option &amp;&amp; typeof option === &quot;object&quot;){myChart.setOption(option, true); }}, 800);<br>",bC="displayName",bD="打开链接",bE="actionInfoDescriptions",bF="javascript:\r\nvar script = document.createElement('script');\r\nscript.type = \"text/javascript\";\r\nscript.src =\"https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js\";\r\ndocument.head.appendChild(script);\r\nsetTimeout(function(){var dom =$('[data-label=degreeChart]').get(0);\r\nvar myChart = echarts.init(dom);\r\noption = {\r\n  tooltip: {\r\n    trigger: 'item'\r\n  },\r\n  legend: {\r\n    top: '5%',\r\n    left: 'center'\r\n  },\r\n  series: [\r\n    {\r\n      name: '学员学历比例',\r\n      type: 'pie',\r\n      radius: ['40%', '70%'],\r\n      avoidLabelOverlap: false,\r\n      itemStyle: {\r\n        borderRadius: 10,\r\n        borderColor: '#fff',\r\n        borderWidth: 2\r\n      },\r\n      label: {\r\n        show: false,\r\n        position: 'center'\r\n      },\r\n      emphasis: {\r\n        label: {\r\n          show: true,\r\n          fontSize: 40,\r\n          fontWeight: 'bold'\r\n        }\r\n      },\r\n      labelLine: {\r\n        show: false\r\n      },\r\n      data: [\r\n        { value: 128, name: '初中' },\r\n        { value: 75, name: '高中' },\r\n        { value: 75, name: '大专' },\r\n        { value: 75, name: '本科' },\r\n        { value: 75, name: '硕士' },\r\n        { value: 75, name: '博士' }\r\n      ]\r\n    }\r\n  ]\r\n};\r\nif (option && typeof option === \"object\"){myChart.setOption(option, true); }}, 800);\r\n",bG="在 当前窗口 打开 javascript:\r\nvar script = document.createElement('script');\r\nscript.type = \"text/javascript\";\r\nscript.src =\"https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js\";\r\ndocument.head.appendChild(script);\r\nsetTimeout(function(){var dom =$('[data-label=degreeChart]').get(0);\r\nvar myChart = echarts.init(dom);\r\noption = {\r\n  tooltip: {\r\n    trigger: 'item'\r\n  },\r\n  legend: {\r\n    top: '5%',\r\n    left: 'center'\r\n  },\r\n  series: [\r\n    {\r\n      name: '学员学历比例',\r\n      type: 'pie',\r\n      radius: ['40%', '70%'],\r\n      avoidLabelOverlap: false,\r\n      itemStyle: {\r\n        borderRadius: 10,\r\n        borderColor: '#fff',\r\n        borderWidth: 2\r\n      },\r\n      label: {\r\n        show: false,\r\n        position: 'center'\r\n      },\r\n      emphasis: {\r\n        label: {\r\n          show: true,\r\n          fontSize: 40,\r\n          fontWeight: 'bold'\r\n        }\r\n      },\r\n      labelLine: {\r\n        show: false\r\n      },\r\n      data: [\r\n        { value: 128, name: '初中' },\r\n        { value: 75, name: '高中' },\r\n        { value: 75, name: '大专' },\r\n        { value: 75, name: '本科' },\r\n        { value: 75, name: '硕士' },\r\n        { value: 75, name: '博士' }\r\n      ]\r\n    }\r\n  ]\r\n};\r\nif (option && typeof option === \"object\"){myChart.setOption(option, true); }}, 800);\r\n",bH="target",bI="targetType",bJ="webUrl",bK="urlLiteral",bL="exprType",bM="stringLiteral",bN="value",bO="localVariables",bP="stos",bQ="includeVariables",bR="linkType",bS="current",bT="在 当前窗口 打开 javascript:<br>var script = document.createElement('script');<br>script.type = &quot;text/javascript&quot;;<br>script.src =&quot;https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js&quot;;<br>document.head.appendChild(script);<br>setTimeout(function(){var dom =$('[data-label=countChart]').get(0);<br>var myChart = echarts.init(dom);<br>option = {<br>&nbsp; xAxis: {<br>&nbsp; &nbsp; type: 'category',<br>&nbsp; &nbsp; data: ['java就业100期', 'java就业101期', 'java就业102期', 'java就业103期', 'java就业104期', 'java就业105期', 'java就业106期']<br>&nbsp; },<br>&nbsp; yAxis: {<br>&nbsp; &nbsp; type: 'value'<br>&nbsp; },<br>&nbsp; series: [<br>&nbsp; &nbsp; {<br>&nbsp; &nbsp; &nbsp; data: [88, 46, 92, 81, 75, 78, 65],<br>&nbsp; &nbsp; &nbsp; type: 'bar'<br>&nbsp; &nbsp; }<br>&nbsp; ]<br>};<br>if (option &amp;&amp; typeof option === &quot;object&quot;){myChart.setOption(option, true); }}, 800);",bU="javascript:\r\nvar script = document.createElement('script');\r\nscript.type = \"text/javascript\";\r\nscript.src =\"https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js\";\r\ndocument.head.appendChild(script);\r\nsetTimeout(function(){var dom =$('[data-label=countChart]').get(0);\r\nvar myChart = echarts.init(dom);\r\noption = {\r\n  xAxis: {\r\n    type: 'category',\r\n    data: ['java就业100期', 'java就业101期', 'java就业102期', 'java就业103期', 'java就业104期', 'java就业105期', 'java就业106期']\r\n  },\r\n  yAxis: {\r\n    type: 'value'\r\n  },\r\n  series: [\r\n    {\r\n      data: [88, 46, 92, 81, 75, 78, 65],\r\n      type: 'bar'\r\n    }\r\n  ]\r\n};\r\nif (option && typeof option === \"object\"){myChart.setOption(option, true); }}, 800);",bV="在 当前窗口 打开 javascript:\r\nvar script = document.createElement('script');\r\nscript.type = \"text/javascript\";\r\nscript.src =\"https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js\";\r\ndocument.head.appendChild(script);\r\nsetTimeout(function(){var dom =$('[data-label=countChart]').get(0);\r\nvar myChart = echarts.init(dom);\r\noption = {\r\n  xAxis: {\r\n    type: 'category',\r\n    data: ['java就业100期', 'java就业101期', 'java就业102期', 'java就业103期', 'java就业104期', 'java就业105期', 'java就业106期']\r\n  },\r\n  yAxis: {\r\n    type: 'value'\r\n  },\r\n  series: [\r\n    {\r\n      data: [88, 46, 92, 81, 75, 78, 65],\r\n      type: 'bar'\r\n    }\r\n  ]\r\n};\r\nif (option && typeof option === \"object\"){myChart.setOption(option, true); }}, 800);",bW="diagram",bX="objects",bY="id",bZ="70222699daa741e38887a281f4bc8ad4",ca="label",cb="friendlyType",cc="左侧菜单",cd="referenceDiagramObject",ce="styleType",cf="visible",cg=true,ch=1370,ci=849,cj="imageOverrides",ck="masterId",cl="ae5e21403c0347f3a21f2ae812e6e058",cm="65ad1ee021f943cda400581efe319d4d",cn="degreeChart",co="矩形",cp="vectorShape",cq="40519e9ec4264601bfb12c514e4f4867",cr=541,cs=449,ct="location",cu="x",cv=873,cw="y",cx=164,cy=0x797979,cz="images",cA="normal~",cB="images/学员信息统计/degreechart_u921.svg",cC="generateCompound",cD="600b042011ca4d1ca50c4b6d7835b45b",cE="4988d43d80b44008a4a415096f1632af",cF=223,cG=42,cH=1025,cI=98,cJ="fontSize",cK="36px",cL="e46fca56f2954365b2c412e9d7c97033",cM=290,cN=446,cO="ec4d826e5a214218b476e08eed96e24b",cP="countChart",cQ=593,cR=236,cS="images/学员信息统计/countchart_u924.svg",cT="f9b24ae5c0ca4d478109a4b7f2ffdf0d",cU="对话框",cV="动态面板",cW="dynamicPanel",cX=1049,cY=1485,cZ="fixedHorizontal",da="fixedMarginHorizontal",db="fixedVertical",dc="middle",dd="fixedMarginVertical",de="fixedKeepInFront",df="scrollbars",dg="verticalAsNeeded",dh="fitToContent",di="propagate",dj="diagrams",dk="47a0bf6792e442d3967868bf1da5b5a9",dl="退出确认",dm="Axure:PanelDiagram",dn=0xFFFFFF,dp="b792a1c32ba9470883bf8808688abfd4",dq=897,dr=691,ds=1596,dt=123,du="masters",dv="ae5e21403c0347f3a21f2ae812e6e058",dw="Axure:Master",dx="9a0a39a0485649d3a1c5b0a0066446ce",dy=200,dz="9ccf6dcb8c5a4b2fab4dd93525dfbe85",dA=0x53E6E6E6,dB="604ee46d9a12425293d463071371403e",dC="内容管理",dD=433,dE=2,dF=112,dG="onDoubleClick",dH="鼠标双击时",dI="linkFrame",dJ="打开 班级管理 在父窗口",dK="在内部框架打开链接",dL="班级管理 在父窗口",dM="parentFrame",dN="班级管理.html",dO="none",dP="04213eef7454426c9fb1ae5a7dd784ed",dQ="展开",dR="295bf20fbd69492ebd3f740f7f84a718",dS="parentDynamicPanel",dT="panelIndex",dU="fontWeight",dV="700",dW="foreGroundFill",dX=0xFF000000,dY="opacity",dZ=1,ea=50,eb="ee0d561981f0497aa5993eaf71a3de39",ec=0xFFF2F2F2,ed="stateStyles",ee="mouseOver",ef=0xFFFF9900,eg="selected",eh=59,ei=177,ej=156,ek="horizontalAlignment",el="left",em="paddingLeft",en="30",eo=0xFFD7D7D7,ep=133,eq="b9857838fe6142238252eebaec5aa230",er="'微软雅黑'",es="400",et=183,eu="50",ev=0xFFFCFCFC,ew="16px",ex="onClick",ey="鼠标单击时",ez="打开 部门管理 在父窗口",eA="部门管理 在父窗口",eB="部门管理.html",eC="tabbable",eD="e988082baf344948b4e9372a2f8d6190",eE=233,eF="打开 员工管理 在父窗口",eG="员工管理 在父窗口",eH="员工管理.html",eI="9c5720e473794292b373461dedf3ea35",eJ=-17,eK="3c33dc6ebb094b38b72c10f8833edb1c",eL=33,eM="420260d0b3a347f6b83d06915af77c6b",eN=83,eO="打开 学员管理 在父窗口",eP="学员管理 在父窗口",eQ="学员管理.html",eR="cca88c282b3c463686ee0bfde9546252",eS=283,eT="aca091ffec214362ac6e96d171049207",eU=333,eV="打开 员工信息统计 在父窗口",eW="员工信息统计 在父窗口",eX="员工信息统计.html",eY="6dbb7b74d6d043abbb512b7c3c4be725",eZ=383,fa="打开 学员信息统计 在父窗口",fb="学员信息统计 在父窗口",fc="c27566e102774d729251426f8dc2db1c",fd=-61,fe="在 当前窗口 打开 首页",ff="首页",fg="首页.html",fh="a14117998fc04f4fa2bd1c1b72409b8b",fi="收起",fj="f8ad1b1492924aa9b2f7d19005143f17",fk=1,fl=0xFF999999,fm="4b7bfc596114427989e10bb0b557d0ce",fn=0xFFF9F9F9,fo="0206acd85ae5409caa5fd56ae8775c00",fp="'FontAwesome'",fq=0xFFBDC3C7,fr=150,fs=16,ft="verticalAlignment",fu="lineSpacing",fv="14px",fw="0129acd185f04062a179a5996c70ab3c",fx="顶部菜单",fy=46,fz="b8a060251dce4215b733759de8533aab",fA="框架",fB="faae812881904966b8cc1803923bea86",fC=56,fD="19px",fE=0xFFE4E4E4,fF=0xFF7F7F7F,fG="d1560bf61327453db8d7b7cf14fe44ea",fH=425,fI=158,fJ=-1,fK=0xFF81D3F8,fL="eb41becbd848468b8f1a91ede77caf07",fM="图像",fN="imageBox",fO="********************************",fP=151,fQ=3,fR="u915~normal~",fS="images/首页/u18.png",fT="3985929dd35d499083e7daf1392a7861",fU=93,fV=1270,fW="paddingRight",fX="打开 登录 在父窗口",fY="登录 在父窗口",fZ="登录.html",ga="815f719662da48a3b04597dbbff6840d",gb=100,gc=1169,gd="在 当前窗口 打开 修改密码",ge="修改密码",gf="修改密码.html",gg="bde0032906fb41708124ddc799c0cfde",gh=23,gi=1272,gj=11,gk="u918~normal~",gl="images/首页/u21.png",gm="59cfb65691914178aa964c623d53fb26",gn=25,go=1171,gp=12,gq="u919~normal~",gr="images/首页/u22.png",gs="b86d9a6e54e2408ea0cc6e4b123f60d6",gt=30,gu=28,gv=-385,gw=365,gx="u920~normal~",gy="images/首页/u23.png",gz="objectPaths",gA="70222699daa741e38887a281f4bc8ad4",gB="scriptId",gC="u897",gD="9a0a39a0485649d3a1c5b0a0066446ce",gE="u898",gF="604ee46d9a12425293d463071371403e",gG="u899",gH="295bf20fbd69492ebd3f740f7f84a718",gI="u900",gJ="b9857838fe6142238252eebaec5aa230",gK="u901",gL="e988082baf344948b4e9372a2f8d6190",gM="u902",gN="9c5720e473794292b373461dedf3ea35",gO="u903",gP="3c33dc6ebb094b38b72c10f8833edb1c",gQ="u904",gR="420260d0b3a347f6b83d06915af77c6b",gS="u905",gT="cca88c282b3c463686ee0bfde9546252",gU="u906",gV="aca091ffec214362ac6e96d171049207",gW="u907",gX="6dbb7b74d6d043abbb512b7c3c4be725",gY="u908",gZ="c27566e102774d729251426f8dc2db1c",ha="u909",hb="f8ad1b1492924aa9b2f7d19005143f17",hc="u910",hd="0206acd85ae5409caa5fd56ae8775c00",he="u911",hf="0129acd185f04062a179a5996c70ab3c",hg="u912",hh="faae812881904966b8cc1803923bea86",hi="u913",hj="d1560bf61327453db8d7b7cf14fe44ea",hk="u914",hl="eb41becbd848468b8f1a91ede77caf07",hm="u915",hn="3985929dd35d499083e7daf1392a7861",ho="u916",hp="815f719662da48a3b04597dbbff6840d",hq="u917",hr="bde0032906fb41708124ddc799c0cfde",hs="u918",ht="59cfb65691914178aa964c623d53fb26",hu="u919",hv="b86d9a6e54e2408ea0cc6e4b123f60d6",hw="u920",hx="65ad1ee021f943cda400581efe319d4d",hy="u921",hz="600b042011ca4d1ca50c4b6d7835b45b",hA="u922",hB="e46fca56f2954365b2c412e9d7c97033",hC="u923",hD="ec4d826e5a214218b476e08eed96e24b",hE="u924",hF="f9b24ae5c0ca4d478109a4b7f2ffdf0d",hG="u925",hH="b792a1c32ba9470883bf8808688abfd4",hI="u926";
return _creator();
})());