$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn)),i,_(j,k,l,k)),bo,_(),bp,_(),bq,_(br,[_(bs,bt,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(A,bB,i,_(j,bC,l,bD),bE,_(bF,bG,bH,bI),E,_(F,G,H,bJ)),bo,_(),bK,_(),bL,bd),_(bs,bM,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(bN,_(F,G,H,bO,bP,bQ),i,_(j,bR,l,bS),A,bT,bE,_(bF,bU,bH,bV),E,_(F,G,H,bW),X,_(F,G,H,bX)),bo,_(),bK,_(),bL,bd),_(bs,bY,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(bN,_(F,G,H,bZ,bP,bQ),i,_(j,ca,l,cb),A,cc,bE,_(bF,cd,bH,ce),X,_(F,G,H,cf),cg,ch),bo,_(),bK,_(),bL,bd),_(bs,ci,bu,h,bv,cj,u,ck,by,ck,bz,bA,z,_(i,_(j,cl,l,cm),cn,_(co,_(A,cp),cq,_(A,cr)),A,cs,bE,_(bF,ct,bH,cu),V,Q,E,_(F,G,H,cv)),cw,bd,bo,_(),bK,_(),cx,cy),_(bs,cz,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(bN,_(F,G,H,I,bP,bQ),i,_(j,cA,l,cB),A,cC,bE,_(bF,cD,bH,cE),E,_(F,G,H,cF),cG,cH),bo,_(),bK,_(),bp,_(cI,_(cJ,cK,cL,[_(cJ,h,cM,h,cN,bd,cO,cP,cQ,[_(cR,cS,cJ,cT,cU,cV,cW,_(cX,_(h,cT)),cY,cZ,da,_(db,r,b,dc,dd,bA))])])),de,bA,bL,bd),_(bs,df,bu,h,bv,dg,u,bx,by,bx,bz,bA,z,_(bN,_(F,G,H,I,bP,bQ),i,_(j,dh,l,cB),A,cC,bE,_(bF,di,bH,cE),E,_(F,G,H,bX),cG,cH),bo,_(),bK,_(),dj,_(dk,dl),bL,bd),_(bs,dm,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(bN,_(F,G,H,dn,bP,bQ),i,_(j,dp,l,dq),A,cC,bE,_(bF,dr,bH,ds),E,_(F,G,H,cv),cg,ch,dt,du),bo,_(),bK,_(),bL,bd),_(bs,dv,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(bN,_(F,G,H,bZ,bP,bQ),i,_(j,ca,l,cb),A,cc,bE,_(bF,cd,bH,ca),X,_(F,G,H,cf),cg,ch),bo,_(),bK,_(),bL,bd),_(bs,dw,bu,h,bv,dx,u,dy,by,dy,bz,bA,z,_(A,dz,i,_(j,dA,l,dB),bE,_(bF,dC,bH,dD),J,null),bo,_(),bK,_(),dj,_(dk,dE)),_(bs,dF,bu,h,bv,dx,u,dy,by,dy,bz,bA,z,_(A,dz,i,_(j,dG,l,dH),bE,_(bF,dI,bH,cA),J,null),bo,_(),bK,_(),dj,_(dk,dJ)),_(bs,dK,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(dL,dM,A,dN,i,_(j,dO,l,dP),bE,_(bF,ct,bH,dQ),cG,dR),bo,_(),bK,_(),bL,bd),_(bs,dS,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(A,dN,i,_(j,dT,l,dU),bE,_(bF,dr,bH,dV),cG,dW),bo,_(),bK,_(),bL,bd)])),dX,_(),dY,_(dZ,_(ea,eb),ec,_(ea,ed),ee,_(ea,ef),eg,_(ea,eh),ei,_(ea,ej),ek,_(ea,el),em,_(ea,en),eo,_(ea,ep),eq,_(ea,er),es,_(ea,et),eu,_(ea,ev),ew,_(ea,ex)));}; 
var b="url",c="登录.html",d="generationDate",e=new Date(1700034921907.44),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="de6e8c5d8c6f45a193a386e123b02601",u="type",v="Axure:Page",w="登录",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="borderFill",Y=0xFF797979,Z="cornerRadius",ba="cornerVisibility",bb="outerShadow",bc="on",bd=false,be="offsetX",bf=5,bg="offsetY",bh="blurRadius",bi="r",bj=0,bk="g",bl="b",bm="a",bn=0.349019607843137,bo="adaptiveStyles",bp="interactionMap",bq="diagram",br="objects",bs="id",bt="cb1532ebc1c248338171f8d824bd51a9",bu="label",bv="friendlyType",bw="矩形",bx="vectorShape",by="styleType",bz="visible",bA=true,bB="40519e9ec4264601bfb12c514e4f4867",bC=1248,bD=606,bE="location",bF="x",bG=11,bH="y",bI=10,bJ=0x1FFFFFF,bK="imageOverrides",bL="generateCompound",bM="cab336b5856d4754b7f8bea15a449078",bN="foreGroundFill",bO=0xFFF2F2F2,bP="opacity",bQ=1,bR=509,bS=382,bT="4b7bfc596114427989e10bb0b557d0ce",bU=657,bV=150,bW=0x1E80FFFF,bX=0xFFD7D7D7,bY="365634d5311b4aab8473790935c4966e",bZ=0xFFAAAAAA,ca=345,cb=40,cc="ee0d561981f0497aa5993eaf71a3de39",cd=766,ce=273,cf=0xFF7F7F7F,cg="horizontalAlignment",ch="left",ci="85a1ff6c89194438bd7e01395f568126",cj="文本框(单行)",ck="textBox",cl=61,cm=26,cn="stateStyles",co="hint",cp="4889d666e8ad4c5e81e59863039a5cc0",cq="disabled",cr="9bd0236217a94d89b0314c8c7fc75f16",cs="ac6e148b6d2a4ceeaf4ccdf56dd34b88",ct=699,cu=280,cv=0xFFFFFF,cw="HideHintOnFocused",cx="placeholderText",cy="请输入广告名称",cz="9a2ad9f634094df8a727161c910d544b",cA=116,cB=41,cC="9ccf6dcb8c5a4b2fab4dd93525dfbe85",cD=745,cE=447,cF=0xFF02A7F0,cG="fontSize",cH="16px",cI="onClick",cJ="description",cK="鼠标单击时",cL="cases",cM="conditionString",cN="isNewIfGroup",cO="caseColorHex",cP="9D33FA",cQ="actions",cR="action",cS="linkFrame",cT="打开 首页 在父窗口",cU="displayName",cV="在内部框架打开链接",cW="actionInfoDescriptions",cX="首页 在父窗口",cY="linkType",cZ="parentFrame",da="target",db="targetType",dc="首页.html",dd="includeVariables",de="tabbable",df="f64e15b9f97c4d63b9c0b0fdb83f7927",dg="形状",dh=119,di=971,dj="images",dk="normal~",dl="images/登录/u932.svg",dm="52d06b328c004dd0b4ee6d916c8c525e",dn=0xFF000000,dp=72,dq=42,dr=688,ds=343,dt="paddingLeft",du="10",dv="7f152e49c2e04b3e86281ecb94bbd514",dw="6e6e96b3e3744fe399c3ee57234fae07",dx="图像",dy="imageBox",dz="********************************",dA=210,dB=66,dC=17,dD=16,dE="images/登录/u935.png",dF="8ad657dfe5cf4103a2a796fdd5d85a2c",dG=557,dH=450,dI=67,dJ="images/登录/u936.png",dK="f2248d14652444058f8a00a5f3c3ef2f",dL="fontWeight",dM="900",dN="4988d43d80b44008a4a415096f1632af",dO=162,dP=28,dQ=82,dR="20px",dS="263641f1ac99459a9c392ae0a4162673",dT=452,dU=47,dV=181,dW="40px",dX="masters",dY="objectPaths",dZ="cb1532ebc1c248338171f8d824bd51a9",ea="scriptId",eb="u927",ec="cab336b5856d4754b7f8bea15a449078",ed="u928",ee="365634d5311b4aab8473790935c4966e",ef="u929",eg="85a1ff6c89194438bd7e01395f568126",eh="u930",ei="9a2ad9f634094df8a727161c910d544b",ej="u931",ek="f64e15b9f97c4d63b9c0b0fdb83f7927",el="u932",em="52d06b328c004dd0b4ee6d916c8c525e",en="u933",eo="7f152e49c2e04b3e86281ecb94bbd514",ep="u934",eq="6e6e96b3e3744fe399c3ee57234fae07",er="u935",es="8ad657dfe5cf4103a2a796fdd5d85a2c",et="u936",eu="f2248d14652444058f8a00a5f3c3ef2f",ev="u937",ew="263641f1ac99459a9c392ae0a4162673",ex="u938";
return _creator();
})());