body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-11px;
  width:1248px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1248px;
  height:606px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.00392156862745098);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u927 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:10px;
  width:1248px;
  height:606px;
  display:flex;
}
#u927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:509px;
  height:382px;
  background:inherit;
  background-color:rgba(128, 255, 255, 0.117647058823529);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#F2F2F2;
}
#u928 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:150px;
  width:509px;
  height:382px;
  display:flex;
  color:#F2F2F2;
}
#u928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u929_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:345px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(127, 127, 127, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u929 {
  border-width:0px;
  position:absolute;
  left:766px;
  top:273px;
  width:345px;
  height:40px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u929 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u929_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u930_input {
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:26px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u930_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:26px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u930_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u930 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:280px;
  width:61px;
  height:26px;
  display:flex;
}
#u930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u930_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u930.disabled {
}
#u931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:41px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u931 {
  border-width:0px;
  position:absolute;
  left:745px;
  top:447px;
  width:116px;
  height:41px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u931 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u932_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:41px;
}
#u932 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:447px;
  width:119px;
  height:41px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u933_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
  text-align:left;
}
#u933 {
  border-width:0px;
  position:absolute;
  left:688px;
  top:343px;
  width:72px;
  height:42px;
  display:flex;
  color:#000000;
  text-align:left;
}
#u933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u934_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:345px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(127, 127, 127, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u934 {
  border-width:0px;
  position:absolute;
  left:766px;
  top:345px;
  width:345px;
  height:40px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:66px;
}
#u935 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:16px;
  width:210px;
  height:66px;
  display:flex;
}
#u935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:557px;
  height:450px;
}
#u936 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:116px;
  width:557px;
  height:450px;
  display:flex;
}
#u936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u937_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Black', 'Arial Normal', 'Arial';
  font-weight:900;
  font-style:normal;
  font-size:20px;
}
#u937 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:82px;
  width:162px;
  height:28px;
  display:flex;
  font-family:'Arial Black', 'Arial Normal', 'Arial';
  font-weight:900;
  font-style:normal;
  font-size:20px;
}
#u937 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u938_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:47px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u938 {
  border-width:0px;
  position:absolute;
  left:688px;
  top:181px;
  width:452px;
  height:47px;
  display:flex;
  font-size:40px;
}
#u938 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
