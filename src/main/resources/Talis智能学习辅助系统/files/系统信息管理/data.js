$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn)),i,_(j,k,l,k)),bo,_(),bp,_(bq,_(br,bs,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,bA,br,bB,bC,bD,bE,_(bF,_(h,bB)),bG,_(bH,r,b,bI,bJ,bK),bL,bM)])])),bN,_(bO,[])),bP,_(),bQ,_());}; 
var b="url",c="系统信息管理.html",d="generationDate",e=new Date(1700034921017.56),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="9d5e3aed073d4a6f9a29b934104f0c3f",u="type",v="Axure:Page",w="系统信息管理",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="borderFill",Y=0xFF797979,Z="cornerRadius",ba="cornerVisibility",bb="outerShadow",bc="on",bd=false,be="offsetX",bf=5,bg="offsetY",bh="blurRadius",bi="r",bj=0,bk="g",bl="b",bm="a",bn=0.349019607843137,bo="adaptiveStyles",bp="interactionMap",bq="onLoad",br="description",bs="页面载入时",bt="cases",bu="conditionString",bv="isNewIfGroup",bw="caseColorHex",bx="9D33FA",by="actions",bz="action",bA="linkWindow",bB="在 当前窗口 打开 部门管理",bC="displayName",bD="打开链接",bE="actionInfoDescriptions",bF="部门管理",bG="target",bH="targetType",bI="部门管理.html",bJ="includeVariables",bK=true,bL="linkType",bM="current",bN="diagram",bO="objects",bP="masters",bQ="objectPaths";
return _creator();
})());