body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:2108px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u868_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:849px;
  background:inherit;
  background-color:rgba(230, 230, 230, 0.325490196078431);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u868 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:849px;
  display:flex;
}
#u868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u869 {
  position:absolute;
  left:2px;
  top:112px;
}
#u869_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:433px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u869_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u870 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:133px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 30px;
  box-sizing:border-box;
  width:100%;
}
#u870_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u870.mouseOver {
}
#u870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u871_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u871 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:183px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 50px;
  box-sizing:border-box;
  width:100%;
}
#u871_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u871.mouseOver {
}
#u871_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u871.selected {
}
#u871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u872 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:233px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 50px;
  box-sizing:border-box;
  width:100%;
}
#u872_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u872.mouseOver {
}
#u872_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u872.selected {
}
#u872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u873_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
  text-align:left;
}
#u873 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-17px;
  width:200px;
  height:50px;
  display:flex;
  color:#000000;
  text-align:left;
}
#u873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 30px;
  box-sizing:border-box;
  width:100%;
}
#u873_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
  text-align:left;
}
#u873.mouseOver {
}
#u873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u874_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u874 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:33px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 50px;
  box-sizing:border-box;
  width:100%;
}
#u874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u875_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u875 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:83px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 50px;
  box-sizing:border-box;
  width:100%;
}
#u875_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u875.mouseOver {
}
#u875_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u875.selected {
}
#u875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u876_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u876 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:283px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 30px;
  box-sizing:border-box;
  width:100%;
}
#u876_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u876.mouseOver {
}
#u876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u877_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u877 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:333px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 50px;
  box-sizing:border-box;
  width:100%;
}
#u877_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u877.mouseOver {
}
#u877_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u877.selected {
}
#u877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u878_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u878 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:383px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 50px;
  box-sizing:border-box;
  width:100%;
}
#u878_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(252, 252, 252, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u878.mouseOver {
}
#u878_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u878.selected {
}
#u878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u879_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
  text-align:left;
}
#u879 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-61px;
  width:200px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#000000;
  text-align:left;
}
#u879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 30px;
  box-sizing:border-box;
  width:100%;
}
#u879_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
  text-align:left;
}
#u879.mouseOver {
}
#u879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u869_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u869_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u880_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(249, 249, 249, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#999999;
  text-align:left;
}
#u880 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  color:#999999;
  text-align:left;
}
#u880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 30px;
  box-sizing:border-box;
  width:100%;
}
#u880_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(249, 249, 249, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#999999;
  text-align:left;
}
#u880.mouseOver {
}
#u880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u881_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'FontAwesome';
  font-weight:400;
  font-style:normal;
  color:#BDC3C7;
  text-align:center;
  line-height:14px;
}
#u881 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:0px;
  width:16px;
  height:50px;
  display:flex;
  font-family:'FontAwesome';
  font-weight:400;
  font-style:normal;
  color:#BDC3C7;
  text-align:center;
  line-height:14px;
}
#u881 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u882 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1px;
  width:1370px;
  height:46px;
}
#u882_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1370px;
  height:46px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-color:rgba(129, 211, 248, 1);
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u882_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u883_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1370px;
  height:56px;
  background:inherit;
  background-color:rgba(127, 127, 127, 1);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:19px;
  color:#FFFFFF;
  line-height:19px;
}
#u883 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1370px;
  height:56px;
  display:flex;
  font-size:19px;
  color:#FFFFFF;
  line-height:19px;
}
#u883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u884_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:36px;
  text-align:left;
}
#u884 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:-1px;
  width:425px;
  height:50px;
  display:flex;
  font-size:36px;
  text-align:left;
}
#u884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u885_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
}
#u885 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:3px;
  width:151px;
  height:42px;
  display:flex;
}
#u885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u886_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u886 {
  border-width:0px;
  position:absolute;
  left:1270px;
  top:1px;
  width:93px;
  height:46px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u886 .text {
  position:absolute;
  align-self:center;
  padding:2px 0px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u887_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u887 {
  border-width:0px;
  position:absolute;
  left:1169px;
  top:1px;
  width:100px;
  height:46px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u887 .text {
  position:absolute;
  align-self:center;
  padding:2px 0px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:23px;
}
#u888 {
  border-width:0px;
  position:absolute;
  left:1272px;
  top:11px;
  width:23px;
  height:23px;
  display:flex;
}
#u888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u889 {
  border-width:0px;
  position:absolute;
  left:1171px;
  top:12px;
  width:25px;
  height:25px;
  display:flex;
}
#u889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:28px;
}
#u890 {
  border-width:0px;
  position:absolute;
  left:-385px;
  top:365px;
  width:30px;
  height:28px;
  display:flex;
}
#u890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:527px;
  height:449px;
}
#u891 {
  border-width:0px;
  position:absolute;
  left:900px;
  top:160px;
  width:527px;
  height:449px;
  display:flex;
}
#u891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u892_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:36px;
}
#u892 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:94px;
  width:223px;
  height:42px;
  display:flex;
  font-size:36px;
}
#u892 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u893_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:36px;
}
#u893 {
  border-width:0px;
  position:absolute;
  left:430px;
  top:94px;
  width:225px;
  height:42px;
  display:flex;
  font-size:36px;
}
#u893 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:593px;
  height:449px;
}
#u894 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:160px;
  width:593px;
  height:449px;
  display:flex;
}
#u894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u895 {
  position:fixed;
  left:50%;
  margin-left:-524.5px;
  top:50%;
  margin-top:-424.5px;
  width:1049px;
  height:849px;
  visibility:hidden;
}
#u895_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1049px;
  height:849px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u895_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u896_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:897px;
  height:691px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u896 {
  border-width:0px;
  position:absolute;
  left:1596px;
  top:123px;
  width:897px;
  height:691px;
  display:flex;
}
#u896 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
