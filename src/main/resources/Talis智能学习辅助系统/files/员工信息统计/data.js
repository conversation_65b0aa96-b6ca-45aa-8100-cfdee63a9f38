$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn)),i,_(j,k,l,k)),bo,_(),bp,_(bq,_(br,bs,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,bA,br,bB,bC,bD,bE,_(bF,_(h,bG)),bH,_(bI,bJ,bK,_(bL,bM,bN,bF,bO,_(),bP,[]),bQ,bd),bR,bS),_(bz,bA,br,bT,bC,bD,bE,_(bU,_(h,bV)),bH,_(bI,bJ,bK,_(bL,bM,bN,bU,bO,_(),bP,[]),bQ,bd),bR,bS)])])),bW,_(bX,[_(bY,bZ,ca,h,cb,cc,u,cd,ce,cd,cf,cg,z,_(i,_(j,ch,l,ci)),bo,_(),cj,_(),ck,cl),_(bY,cm,ca,cn,cb,co,u,cp,ce,cp,cf,cg,z,_(A,cq,i,_(j,cr,l,cs),ct,_(cu,cv,cw,cx),X,_(F,G,H,cy)),bo,_(),cj,_(),cz,_(cA,cB),cC,bd),_(bY,cD,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(A,cE,i,_(j,cF,l,cG),ct,_(cu,cH,cw,cI),cJ,cK),bo,_(),cj,_(),cC,bd),_(bY,cL,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(A,cE,i,_(j,cM,l,cG),ct,_(cu,cN,cw,cI),cJ,cK),bo,_(),cj,_(),cC,bd),_(bY,cO,ca,cP,cb,cQ,u,cp,ce,cp,cf,cg,z,_(A,cq,i,_(j,cR,l,cs),ct,_(cu,cS,cw,cx),X,_(F,G,H,cy)),bo,_(),cj,_(),cz,_(cA,cT),cC,bd),_(bY,cU,ca,cV,cb,cW,u,cX,ce,cX,cf,bd,z,_(i,_(j,cY,l,ci),ct,_(cu,cZ,cw,k),cf,bd),bo,_(),cj,_(),da,D,db,k,dc,dd,de,k,df,cg,dg,dh,di,bd,dj,bd,dk,[_(bY,dl,ca,dm,u,dn,bX,[],z,_(E,_(F,G,H,dp),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_())]),_(bY,dq,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(A,cE,i,_(j,dr,l,ds),ct,_(cu,dt,cw,du)),bo,_(),cj,_(),cC,bd)])),dv,_(dw,_(s,dw,u,dx,g,cc,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn)),i,_(j,k,l,k)),m,[],bp,_(),bW,_(bX,[_(bY,dy,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(i,_(j,dz,l,ci),A,dA,E,_(F,G,H,dB)),bo,_(),cj,_(),cC,bd),_(bY,dC,ca,dD,cb,cW,u,cX,ce,cX,cf,cg,z,_(i,_(j,dz,l,dE),ct,_(cu,dF,cw,dG)),bo,_(),cj,_(),bp,_(dH,_(br,dI,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dJ,br,dK,bC,dL,bE,_(dM,_(h,dK)),bR,dN,bH,_(bI,r,b,dO,bQ,cg))])])),dg,dP,di,cg,dj,bd,dk,[_(bY,dQ,ca,dR,u,dn,bX,[_(bY,dS,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,z,_(dV,dW,dX,_(F,G,H,dY,dZ,ea),i,_(j,dz,l,eb),A,ec,X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,eo,E,_(F,G,H,ep),ct,_(cu,k,cw,eq)),bo,_(),cj,_(),cC,bd),_(bY,er,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,eh,cg,z,_(T,es,dV,et,i,_(j,dz,l,eb),A,ec,ct,_(cu,k,cw,eu),X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_(dX,_(F,G,H,eg,dZ,ea),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,ev,E,_(F,G,H,ew),cJ,ex),bo,_(),cj,_(),bp,_(ey,_(br,ez,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dJ,br,eA,bC,dL,bE,_(eB,_(h,eA)),bR,dN,bH,_(bI,r,b,eC,bQ,cg))])])),eD,cg,cC,bd),_(bY,eE,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,z,_(T,es,dV,et,i,_(j,dz,l,eb),A,ec,ct,_(cu,k,cw,eF),X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_(dX,_(F,G,H,eg,dZ,ea),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,ev,E,_(F,G,H,ew),cJ,ex),bo,_(),cj,_(),bp,_(ey,_(br,ez,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dJ,br,eG,bC,dL,bE,_(eH,_(h,eG)),bR,dN,bH,_(bI,r,b,eI,bQ,cg))])])),eD,cg,cC,bd),_(bY,eJ,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,z,_(dX,_(F,G,H,dY,dZ,ea),i,_(j,dz,l,eb),A,ec,X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,eo,E,_(F,G,H,ep),ct,_(cu,k,cw,eK)),bo,_(),cj,_(),cC,bd),_(bY,eL,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,eh,cg,z,_(T,es,dV,et,i,_(j,dz,l,eb),A,ec,ct,_(cu,k,cw,eM),X,_(F,G,H,ed),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,ev,E,_(F,G,H,ew),cJ,ex),bo,_(),cj,_(),bp,_(ey,_(br,ez,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dJ,br,dK,bC,dL,bE,_(dM,_(h,dK)),bR,dN,bH,_(bI,r,b,dO,bQ,cg))])])),eD,cg,cC,bd),_(bY,eN,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,z,_(T,es,dV,et,i,_(j,dz,l,eb),A,ec,ct,_(cu,k,cw,eO),X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_(dX,_(F,G,H,eg,dZ,ea),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,ev,E,_(F,G,H,ew),cJ,ex),bo,_(),cj,_(),bp,_(ey,_(br,ez,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dJ,br,eP,bC,dL,bE,_(eQ,_(h,eP)),bR,dN,bH,_(bI,r,b,eR,bQ,cg))])])),eD,cg,cC,bd),_(bY,eS,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,z,_(dV,dW,dX,_(F,G,H,dY,dZ,ea),i,_(j,dz,l,eb),A,ec,X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,eo,E,_(F,G,H,ep),ct,_(cu,k,cw,eT)),bo,_(),cj,_(),cC,bd),_(bY,eU,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,eh,cg,z,_(T,es,dV,et,i,_(j,dz,l,eb),A,ec,ct,_(cu,k,cw,eV),X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_(dX,_(F,G,H,eg,dZ,ea),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,ev,E,_(F,G,H,ew),cJ,ex),bo,_(),cj,_(),bp,_(ey,_(br,ez,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dJ,br,eW,bC,dL,bE,_(eX,_(h,eW)),bR,dN,bH,_(bI,r,b,c,bQ,cg))])])),eD,cg,cC,bd),_(bY,eY,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,z,_(T,es,dV,et,i,_(j,dz,l,eb),A,ec,ct,_(cu,k,cw,eZ),X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_(dX,_(F,G,H,eg,dZ,ea),E,_(F,G,H,I))),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,ev,E,_(F,G,H,ew),cJ,ex),bo,_(),cj,_(),bp,_(ey,_(br,ez,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dJ,br,fa,bC,dL,bE,_(fb,_(h,fa)),bR,dN,bH,_(bI,r,b,fc,bQ,cg))])])),eD,cg,cC,bd),_(bY,fd,ca,h,cb,co,dT,dC,dU,bj,u,cp,ce,cp,cf,cg,z,_(dX,_(F,G,H,dY,dZ,ea),i,_(j,dz,l,eb),A,ec,X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,eo,cJ,ex,E,_(F,G,H,ep),ct,_(cu,k,cw,fe)),bo,_(),cj,_(),bp,_(ey,_(br,ez,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,bA,br,ff,bC,bD,bE,_(fg,_(h,ff)),bH,_(bI,r,b,fh,bQ,cg),bR,bS)])])),eD,cg,cC,bd)],z,_(E,_(F,G,H,dp),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_()),_(bY,fi,ca,fj,u,dn,bX,[_(bY,fk,ca,h,cb,co,dT,dC,dU,fl,u,cp,ce,cp,cf,cg,z,_(dX,_(F,G,H,fm,dZ,ea),i,_(j,dz,l,eb),A,fn,X,_(F,G,H,ed),ee,_(ef,_(dX,_(F,G,H,eg,dZ,ea)),eh,_()),bb,_(bc,bd,be,k,bg,bf,bh,k,H,_(bi,ei,bk,ej,bl,ek,bm,ea)),el,em,en,eo,E,_(F,G,H,fo)),bo,_(),cj,_(),cC,bd),_(bY,fp,ca,h,cb,co,dT,dC,dU,fl,u,cp,ce,cp,cf,cg,z,_(T,fq,dV,et,dX,_(F,G,H,fr,dZ,ea),ct,_(cu,fs,cw,k),i,_(j,ft,l,eb),A,cE,el,D,fu,dd,fv,fw),bo,_(),cj,_(),cC,bd)],z,_(E,_(F,G,H,dp),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_())]),_(bY,fx,ca,fy,cb,cW,u,cX,ce,cX,cf,cg,z,_(i,_(j,ch,l,fz),ct,_(cu,k,cw,ea)),bo,_(),cj,_(),dg,dP,di,bd,dj,bd,dk,[_(bY,fA,ca,fB,u,dn,bX,[_(bY,fC,ca,h,cb,co,dT,fx,dU,bj,u,cp,ce,cp,cf,cg,z,_(dX,_(F,G,H,I,dZ,ea),i,_(j,ch,l,fD),A,ec,cJ,fE,fv,fE,ee,_(ef,_()),X,_(F,G,H,fF),Z,Q,V,Q,E,_(F,G,H,fG)),bo,_(),cj,_(),cC,bd),_(bY,fH,ca,h,cb,co,dT,fx,dU,bj,u,cp,ce,cp,cf,cg,z,_(i,_(j,fI,l,eb),A,dA,ct,_(cu,fJ,cw,fK),E,_(F,G,H,dp),el,em,cJ,cK),bo,_(),cj,_(),cC,bd)],z,_(E,_(F,G,H,fL),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_())]),_(bY,fM,ca,h,cb,fN,u,fO,ce,fO,cf,cg,z,_(A,fP,i,_(j,fQ,l,cG),ct,_(cu,dF,cw,fR),J,null),bo,_(),cj,_(),cz,_(fS,fT)),_(bY,fU,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(dX,_(F,G,H,I,dZ,ea),i,_(j,fV,l,fz),A,fn,ct,_(cu,fW,cw,ea),cJ,fw,E,_(F,G,H,dp),fX,Q),bo,_(),cj,_(),bp,_(ey,_(br,ez,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,dJ,br,fY,bC,dL,bE,_(fZ,_(h,fY)),bR,dN,bH,_(bI,r,b,ga,bQ,cg))])])),eD,cg,cC,bd),_(bY,gb,ca,h,cb,co,u,cp,ce,cp,cf,cg,z,_(dX,_(F,G,H,I,dZ,ea),i,_(j,gc,l,fz),A,fn,ct,_(cu,gd,cw,ea),cJ,fw,E,_(F,G,H,dp),fX,Q),bo,_(),cj,_(),bp,_(ey,_(br,ez,bt,[_(br,h,bu,h,bv,bd,bw,bx,by,[_(bz,bA,br,ge,bC,bD,bE,_(gf,_(h,ge)),bH,_(bI,r,b,gg,bQ,cg),bR,bS)])])),eD,cg,cC,bd),_(bY,gh,ca,h,cb,fN,u,fO,ce,fO,cf,cg,z,_(A,fP,i,_(j,gi,l,gi),ct,_(cu,gj,cw,gk),J,null),bo,_(),cj,_(),cz,_(gl,gm)),_(bY,gn,ca,h,cb,fN,u,fO,ce,fO,cf,cg,z,_(A,fP,i,_(j,go,l,go),ct,_(cu,gp,cw,gq),J,null),bo,_(),cj,_(),cz,_(gr,gs)),_(bY,gt,ca,h,cb,fN,u,fO,ce,fO,cf,cg,z,_(A,fP,i,_(j,gu,l,gv),ct,_(cu,gw,cw,gx),J,null),bo,_(),cj,_(),cz,_(gy,gz))]))),gA,_(gB,_(gC,gD,gE,_(gC,gF),gG,_(gC,gH),gI,_(gC,gJ),gK,_(gC,gL),gM,_(gC,gN),gO,_(gC,gP),gQ,_(gC,gR),gS,_(gC,gT),gU,_(gC,gV),gW,_(gC,gX),gY,_(gC,gZ),ha,_(gC,hb),hc,_(gC,hd),he,_(gC,hf),hg,_(gC,hh),hi,_(gC,hj),hk,_(gC,hl),hm,_(gC,hn),ho,_(gC,hp),hq,_(gC,hr),hs,_(gC,ht),hu,_(gC,hv),hw,_(gC,hx)),hy,_(gC,hz),hA,_(gC,hB),hC,_(gC,hD),hE,_(gC,hF),hG,_(gC,hH),hI,_(gC,hJ)));}; 
var b="url",c="员工信息统计.html",d="generationDate",e=new Date(1700034921828.64),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="d2197a921d6543ee878df4886c0b59e5",u="type",v="Axure:Page",w="员工信息统计",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="borderFill",Y=0xFF797979,Z="cornerRadius",ba="cornerVisibility",bb="outerShadow",bc="on",bd=false,be="offsetX",bf=5,bg="offsetY",bh="blurRadius",bi="r",bj=0,bk="g",bl="b",bm="a",bn=0.349019607843137,bo="adaptiveStyles",bp="interactionMap",bq="onLoad",br="description",bs="页面载入时",bt="cases",bu="conditionString",bv="isNewIfGroup",bw="caseColorHex",bx="9D33FA",by="actions",bz="action",bA="linkWindow",bB="在 当前窗口 打开 javascript:<br>var script = document.createElement('script');<br>script.type = &quot;text/javascript&quot;;<br>script.src =&quot;https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js&quot;;<br>document.head.appendChild(script);<br>setTimeout(function(){var dom =$('[data-label=genderChart]').get(0);<br>var myChart = echarts.init(dom);<br>option = {<br>&nbsp; tooltip: {<br>&nbsp; &nbsp; trigger: 'item'<br>&nbsp; },<br>&nbsp; legend: {<br>&nbsp; &nbsp; top: '5%',<br>&nbsp; &nbsp; left: 'center'<br>&nbsp; },<br>&nbsp; series: [<br>&nbsp; &nbsp; {<br>&nbsp; &nbsp; &nbsp; name: '员工性别比例',<br>&nbsp; &nbsp; &nbsp; type: 'pie',<br>&nbsp; &nbsp; &nbsp; radius: ['40%', '70%'],<br>&nbsp; &nbsp; &nbsp; avoidLabelOverlap: false,<br>&nbsp; &nbsp; &nbsp; itemStyle: {<br>&nbsp; &nbsp; &nbsp; &nbsp; borderRadius: 10,<br>&nbsp; &nbsp; &nbsp; &nbsp; borderColor: '#fff',<br>&nbsp; &nbsp; &nbsp; &nbsp; borderWidth: 2<br>&nbsp; &nbsp; &nbsp; },<br>&nbsp; &nbsp; &nbsp; label: {<br>&nbsp; &nbsp; &nbsp; &nbsp; show: false,<br>&nbsp; &nbsp; &nbsp; &nbsp; position: 'center'<br>&nbsp; &nbsp; &nbsp; },<br>&nbsp; &nbsp; &nbsp; emphasis: {<br>&nbsp; &nbsp; &nbsp; &nbsp; label: {<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; show: true,<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; fontSize: 40,<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; fontWeight: 'bold'<br>&nbsp; &nbsp; &nbsp; &nbsp; }<br>&nbsp; &nbsp; &nbsp; },<br>&nbsp; &nbsp; &nbsp; labelLine: {<br>&nbsp; &nbsp; &nbsp; &nbsp; show: false<br>&nbsp; &nbsp; &nbsp; },<br>&nbsp; &nbsp; &nbsp; data: [<br>&nbsp; &nbsp; &nbsp; &nbsp; { value: 128, name: '男性' },<br>&nbsp; &nbsp; &nbsp; &nbsp; { value: 75, name: '女性' }<br>&nbsp; &nbsp; &nbsp; ]<br>&nbsp; &nbsp; }<br>&nbsp; ]<br>};<br>if (option &amp;&amp; typeof option === &quot;object&quot;){myChart.setOption(option, true); }}, 800);<br>",bC="displayName",bD="打开链接",bE="actionInfoDescriptions",bF="javascript:\r\nvar script = document.createElement('script');\r\nscript.type = \"text/javascript\";\r\nscript.src =\"https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js\";\r\ndocument.head.appendChild(script);\r\nsetTimeout(function(){var dom =$('[data-label=genderChart]').get(0);\r\nvar myChart = echarts.init(dom);\r\noption = {\r\n  tooltip: {\r\n    trigger: 'item'\r\n  },\r\n  legend: {\r\n    top: '5%',\r\n    left: 'center'\r\n  },\r\n  series: [\r\n    {\r\n      name: '员工性别比例',\r\n      type: 'pie',\r\n      radius: ['40%', '70%'],\r\n      avoidLabelOverlap: false,\r\n      itemStyle: {\r\n        borderRadius: 10,\r\n        borderColor: '#fff',\r\n        borderWidth: 2\r\n      },\r\n      label: {\r\n        show: false,\r\n        position: 'center'\r\n      },\r\n      emphasis: {\r\n        label: {\r\n          show: true,\r\n          fontSize: 40,\r\n          fontWeight: 'bold'\r\n        }\r\n      },\r\n      labelLine: {\r\n        show: false\r\n      },\r\n      data: [\r\n        { value: 128, name: '男性' },\r\n        { value: 75, name: '女性' }\r\n      ]\r\n    }\r\n  ]\r\n};\r\nif (option && typeof option === \"object\"){myChart.setOption(option, true); }}, 800);\r\n",bG="在 当前窗口 打开 javascript:\r\nvar script = document.createElement('script');\r\nscript.type = \"text/javascript\";\r\nscript.src =\"https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js\";\r\ndocument.head.appendChild(script);\r\nsetTimeout(function(){var dom =$('[data-label=genderChart]').get(0);\r\nvar myChart = echarts.init(dom);\r\noption = {\r\n  tooltip: {\r\n    trigger: 'item'\r\n  },\r\n  legend: {\r\n    top: '5%',\r\n    left: 'center'\r\n  },\r\n  series: [\r\n    {\r\n      name: '员工性别比例',\r\n      type: 'pie',\r\n      radius: ['40%', '70%'],\r\n      avoidLabelOverlap: false,\r\n      itemStyle: {\r\n        borderRadius: 10,\r\n        borderColor: '#fff',\r\n        borderWidth: 2\r\n      },\r\n      label: {\r\n        show: false,\r\n        position: 'center'\r\n      },\r\n      emphasis: {\r\n        label: {\r\n          show: true,\r\n          fontSize: 40,\r\n          fontWeight: 'bold'\r\n        }\r\n      },\r\n      labelLine: {\r\n        show: false\r\n      },\r\n      data: [\r\n        { value: 128, name: '男性' },\r\n        { value: 75, name: '女性' }\r\n      ]\r\n    }\r\n  ]\r\n};\r\nif (option && typeof option === \"object\"){myChart.setOption(option, true); }}, 800);\r\n",bH="target",bI="targetType",bJ="webUrl",bK="urlLiteral",bL="exprType",bM="stringLiteral",bN="value",bO="localVariables",bP="stos",bQ="includeVariables",bR="linkType",bS="current",bT="在 当前窗口 打开 javascript:<br>var script = document.createElement('script');<br>script.type = &quot;text/javascript&quot;;<br>script.src =&quot;https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js&quot;;<br>document.head.appendChild(script);<br>setTimeout(function(){var dom =$('[data-label=jobChart]').get(0);<br>var myChart = echarts.init(dom);<br>option = {<br>&nbsp; xAxis: {<br>&nbsp; &nbsp; type: 'category',<br>&nbsp; &nbsp; data: ['教研主管', '学工主管', '其他', '班主任', '咨询师', '讲师']<br>&nbsp; },<br>&nbsp; yAxis: {<br>&nbsp; &nbsp; type: 'value'<br>&nbsp; },<br>&nbsp; series: [<br>&nbsp; &nbsp; {<br>&nbsp; &nbsp; &nbsp; data: [1, 1, 2, 6, 8, 13],<br>&nbsp; &nbsp; &nbsp; type: 'bar'<br>&nbsp; &nbsp; }<br>&nbsp; ]<br>};<br>if (option &amp;&amp; typeof option === &quot;object&quot;){myChart.setOption(option, true); }}, 800);<br>",bU="javascript:\r\nvar script = document.createElement('script');\r\nscript.type = \"text/javascript\";\r\nscript.src =\"https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js\";\r\ndocument.head.appendChild(script);\r\nsetTimeout(function(){var dom =$('[data-label=jobChart]').get(0);\r\nvar myChart = echarts.init(dom);\r\noption = {\r\n  xAxis: {\r\n    type: 'category',\r\n    data: ['教研主管', '学工主管', '其他', '班主任', '咨询师', '讲师']\r\n  },\r\n  yAxis: {\r\n    type: 'value'\r\n  },\r\n  series: [\r\n    {\r\n      data: [1, 1, 2, 6, 8, 13],\r\n      type: 'bar'\r\n    }\r\n  ]\r\n};\r\nif (option && typeof option === \"object\"){myChart.setOption(option, true); }}, 800);\r\n",bV="在 当前窗口 打开 javascript:\r\nvar script = document.createElement('script');\r\nscript.type = \"text/javascript\";\r\nscript.src =\"https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js\";\r\ndocument.head.appendChild(script);\r\nsetTimeout(function(){var dom =$('[data-label=jobChart]').get(0);\r\nvar myChart = echarts.init(dom);\r\noption = {\r\n  xAxis: {\r\n    type: 'category',\r\n    data: ['教研主管', '学工主管', '其他', '班主任', '咨询师', '讲师']\r\n  },\r\n  yAxis: {\r\n    type: 'value'\r\n  },\r\n  series: [\r\n    {\r\n      data: [1, 1, 2, 6, 8, 13],\r\n      type: 'bar'\r\n    }\r\n  ]\r\n};\r\nif (option && typeof option === \"object\"){myChart.setOption(option, true); }}, 800);\r\n",bW="diagram",bX="objects",bY="id",bZ="7e627166c1334516867a730eb7a90074",ca="label",cb="friendlyType",cc="左侧菜单",cd="referenceDiagramObject",ce="styleType",cf="visible",cg=true,ch=1370,ci=849,cj="imageOverrides",ck="masterId",cl="ae5e21403c0347f3a21f2ae812e6e058",cm="a7642a5d0b024af28147cc75252c5837",cn="genderChart",co="矩形",cp="vectorShape",cq="40519e9ec4264601bfb12c514e4f4867",cr=527,cs=449,ct="location",cu="x",cv=900,cw="y",cx=160,cy=0x797979,cz="images",cA="normal~",cB="images/员工信息统计/genderchart_u891.svg",cC="generateCompound",cD="cef9d6bc587c4916b3400ee96f56e054",cE="4988d43d80b44008a4a415096f1632af",cF=223,cG=42,cH=1052,cI=94,cJ="fontSize",cK="36px",cL="df52b9d46e5a4adc8998fd0a19c69fee",cM=225,cN=430,cO="b825d3fa8fb44e319464b4a0d6e0df98",cP="jobChart",cQ="形状",cR=593,cS=220,cT="images/员工信息统计/jobchart_u894.svg",cU="9aab11ef8fad4044af03b9f5d14e8b14",cV="对话框",cW="动态面板",cX="dynamicPanel",cY=1049,cZ=1485,da="fixedHorizontal",db="fixedMarginHorizontal",dc="fixedVertical",dd="middle",de="fixedMarginVertical",df="fixedKeepInFront",dg="scrollbars",dh="verticalAsNeeded",di="fitToContent",dj="propagate",dk="diagrams",dl="0df7d9afe0c444ef9d31a2f69a8ac31a",dm="退出确认",dn="Axure:PanelDiagram",dp=0xFFFFFF,dq="d57ad7d0e4ea4f3c84bb6e46deb0440c",dr=897,ds=691,dt=1596,du=123,dv="masters",dw="ae5e21403c0347f3a21f2ae812e6e058",dx="Axure:Master",dy="9a0a39a0485649d3a1c5b0a0066446ce",dz=200,dA="9ccf6dcb8c5a4b2fab4dd93525dfbe85",dB=0x53E6E6E6,dC="604ee46d9a12425293d463071371403e",dD="内容管理",dE=433,dF=2,dG=112,dH="onDoubleClick",dI="鼠标双击时",dJ="linkFrame",dK="打开 班级管理 在父窗口",dL="在内部框架打开链接",dM="班级管理 在父窗口",dN="parentFrame",dO="班级管理.html",dP="none",dQ="04213eef7454426c9fb1ae5a7dd784ed",dR="展开",dS="295bf20fbd69492ebd3f740f7f84a718",dT="parentDynamicPanel",dU="panelIndex",dV="fontWeight",dW="700",dX="foreGroundFill",dY=0xFF000000,dZ="opacity",ea=1,eb=50,ec="ee0d561981f0497aa5993eaf71a3de39",ed=0xFFF2F2F2,ee="stateStyles",ef="mouseOver",eg=0xFFFF9900,eh="selected",ei=59,ej=177,ek=156,el="horizontalAlignment",em="left",en="paddingLeft",eo="30",ep=0xFFD7D7D7,eq=133,er="b9857838fe6142238252eebaec5aa230",es="'微软雅黑'",et="400",eu=183,ev="50",ew=0xFFFCFCFC,ex="16px",ey="onClick",ez="鼠标单击时",eA="打开 部门管理 在父窗口",eB="部门管理 在父窗口",eC="部门管理.html",eD="tabbable",eE="e988082baf344948b4e9372a2f8d6190",eF=233,eG="打开 员工管理 在父窗口",eH="员工管理 在父窗口",eI="员工管理.html",eJ="9c5720e473794292b373461dedf3ea35",eK=-17,eL="3c33dc6ebb094b38b72c10f8833edb1c",eM=33,eN="420260d0b3a347f6b83d06915af77c6b",eO=83,eP="打开 学员管理 在父窗口",eQ="学员管理 在父窗口",eR="学员管理.html",eS="cca88c282b3c463686ee0bfde9546252",eT=283,eU="aca091ffec214362ac6e96d171049207",eV=333,eW="打开 员工信息统计 在父窗口",eX="员工信息统计 在父窗口",eY="6dbb7b74d6d043abbb512b7c3c4be725",eZ=383,fa="打开 学员信息统计 在父窗口",fb="学员信息统计 在父窗口",fc="学员信息统计.html",fd="c27566e102774d729251426f8dc2db1c",fe=-61,ff="在 当前窗口 打开 首页",fg="首页",fh="首页.html",fi="a14117998fc04f4fa2bd1c1b72409b8b",fj="收起",fk="f8ad1b1492924aa9b2f7d19005143f17",fl=1,fm=0xFF999999,fn="4b7bfc596114427989e10bb0b557d0ce",fo=0xFFF9F9F9,fp="0206acd85ae5409caa5fd56ae8775c00",fq="'FontAwesome'",fr=0xFFBDC3C7,fs=150,ft=16,fu="verticalAlignment",fv="lineSpacing",fw="14px",fx="0129acd185f04062a179a5996c70ab3c",fy="顶部菜单",fz=46,fA="b8a060251dce4215b733759de8533aab",fB="框架",fC="faae812881904966b8cc1803923bea86",fD=56,fE="19px",fF=0xFFE4E4E4,fG=0xFF7F7F7F,fH="d1560bf61327453db8d7b7cf14fe44ea",fI=425,fJ=158,fK=-1,fL=0xFF81D3F8,fM="eb41becbd848468b8f1a91ede77caf07",fN="图像",fO="imageBox",fP="********************************",fQ=151,fR=3,fS="u885~normal~",fT="images/首页/u18.png",fU="3985929dd35d499083e7daf1392a7861",fV=93,fW=1270,fX="paddingRight",fY="打开 登录 在父窗口",fZ="登录 在父窗口",ga="登录.html",gb="815f719662da48a3b04597dbbff6840d",gc=100,gd=1169,ge="在 当前窗口 打开 修改密码",gf="修改密码",gg="修改密码.html",gh="bde0032906fb41708124ddc799c0cfde",gi=23,gj=1272,gk=11,gl="u888~normal~",gm="images/首页/u21.png",gn="59cfb65691914178aa964c623d53fb26",go=25,gp=1171,gq=12,gr="u889~normal~",gs="images/首页/u22.png",gt="b86d9a6e54e2408ea0cc6e4b123f60d6",gu=30,gv=28,gw=-385,gx=365,gy="u890~normal~",gz="images/首页/u23.png",gA="objectPaths",gB="7e627166c1334516867a730eb7a90074",gC="scriptId",gD="u867",gE="9a0a39a0485649d3a1c5b0a0066446ce",gF="u868",gG="604ee46d9a12425293d463071371403e",gH="u869",gI="295bf20fbd69492ebd3f740f7f84a718",gJ="u870",gK="b9857838fe6142238252eebaec5aa230",gL="u871",gM="e988082baf344948b4e9372a2f8d6190",gN="u872",gO="9c5720e473794292b373461dedf3ea35",gP="u873",gQ="3c33dc6ebb094b38b72c10f8833edb1c",gR="u874",gS="420260d0b3a347f6b83d06915af77c6b",gT="u875",gU="cca88c282b3c463686ee0bfde9546252",gV="u876",gW="aca091ffec214362ac6e96d171049207",gX="u877",gY="6dbb7b74d6d043abbb512b7c3c4be725",gZ="u878",ha="c27566e102774d729251426f8dc2db1c",hb="u879",hc="f8ad1b1492924aa9b2f7d19005143f17",hd="u880",he="0206acd85ae5409caa5fd56ae8775c00",hf="u881",hg="0129acd185f04062a179a5996c70ab3c",hh="u882",hi="faae812881904966b8cc1803923bea86",hj="u883",hk="d1560bf61327453db8d7b7cf14fe44ea",hl="u884",hm="eb41becbd848468b8f1a91ede77caf07",hn="u885",ho="3985929dd35d499083e7daf1392a7861",hp="u886",hq="815f719662da48a3b04597dbbff6840d",hr="u887",hs="bde0032906fb41708124ddc799c0cfde",ht="u888",hu="59cfb65691914178aa964c623d53fb26",hv="u889",hw="b86d9a6e54e2408ea0cc6e4b123f60d6",hx="u890",hy="a7642a5d0b024af28147cc75252c5837",hz="u891",hA="cef9d6bc587c4916b3400ee96f56e054",hB="u892",hC="df52b9d46e5a4adc8998fd0a19c69fee",hD="u893",hE="b825d3fa8fb44e319464b4a0d6e0df98",hF="u894",hG="9aab11ef8fad4044af03b9f5d14e8b14",hH="u895",hI="d57ad7d0e4ea4f3c84bb6e46deb0440c",hJ="u896";
return _creator();
})());