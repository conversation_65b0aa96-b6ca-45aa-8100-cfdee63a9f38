<!DOCTYPE html>
<html>
  <head>
    <title>员工信息统计</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/员工信息统计/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/员工信息统计/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (左侧菜单) -->

      <!-- Unnamed (矩形) -->
      <div id="u868" class="ax_default box_2">
        <div id="u868_div" class=""></div>
        <div id="u868_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 内容管理 (动态面板) -->
      <div id="u869" class="ax_default" data-label="内容管理">
        <div id="u869_state0" class="panel_state" data-label="展开" style="">
          <div id="u869_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u870" class="ax_default _默认样式">
              <div id="u870_div" class=""></div>
              <div id="u870_text" class="text ">
                <p style="font-size:14px;"><span style="font-size:16px;">&nbsp;○ 系统信息管理&nbsp; &nbsp; </span><span>&gt;&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u871" class="ax_default _默认样式 selected" selectiongroup="u867二级菜单">
              <div id="u871_div" class="selected"></div>
              <div id="u871_text" class="text ">
                <p><span>部门管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u872" class="ax_default _默认样式" selectiongroup="u867二级菜单">
              <div id="u872_div" class=""></div>
              <div id="u872_text" class="text ">
                <p><span>员工管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u873" class="ax_default _默认样式">
              <div id="u873_div" class=""></div>
              <div id="u873_text" class="text ">
                <p style="font-size:14px;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;font-size:16px;">○</span><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;font-size:16px;"> 班级学员管理&nbsp; &nbsp; </span><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;">&gt;&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u874" class="ax_default _默认样式 selected" selectiongroup="u867二级菜单">
              <div id="u874_div" class="selected"></div>
              <div id="u874_text" class="text ">
                <p><span>班级管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u875" class="ax_default _默认样式" selectiongroup="u867二级菜单">
              <div id="u875_div" class=""></div>
              <div id="u875_text" class="text ">
                <p><span>学员管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u876" class="ax_default _默认样式">
              <div id="u876_div" class=""></div>
              <div id="u876_text" class="text ">
                <p style="font-size:14px;"><span style="font-size:16px;">&nbsp;○ 数据统计管理&nbsp; &nbsp; </span><span>&gt;&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u877" class="ax_default _默认样式 selected" selectiongroup="u867二级菜单">
              <div id="u877_div" class="selected"></div>
              <div id="u877_text" class="text ">
                <p><span>员工信息统计</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u878" class="ax_default _默认样式" selectiongroup="u867二级菜单">
              <div id="u878_div" class=""></div>
              <div id="u878_text" class="text ">
                <p><span>学员信息统计</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u879" class="ax_default _默认样式">
              <div id="u879_div" class=""></div>
              <div id="u879_text" class="text ">
                <p><span style="font-family:'Arial Normal', 'Arial';font-weight:400;">○</span><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;"> 首页</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u869_state1" class="panel_state" data-label="收起" style="visibility: hidden;">
          <div id="u869_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u880" class="ax_default box_1">
              <div id="u880_div" class=""></div>
              <div id="u880_text" class="text ">
                <p style="font-size:14px;"><span style="font-family:'FontAwesome';font-weight:400;font-style:normal;font-size:16px;"> </span><span style="font-family:'微软雅黑';font-weight:400;">内容管理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u881" class="ax_default _文本">
              <div id="u881_div" class=""></div>
              <div id="u881_text" class="text ">
                <p><span></span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 顶部菜单 (动态面板) -->
      <div id="u882" class="ax_default" data-label="顶部菜单">
        <div id="u882_state0" class="panel_state" data-label="框架" style="">
          <div id="u882_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u883" class="ax_default _默认样式">
              <div id="u883_div" class=""></div>
              <div id="u883_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u884" class="ax_default box_2">
              <div id="u884_div" class=""></div>
              <div id="u884_text" class="text ">
                <p><span style="font-family:'Consolas Bold', 'Consolas Regular', 'Consolas';font-weight:700;color:#FFFFFF;">tlias</span><span style="font-family:'华文彩云';font-weight:400;"> </span><span style="font-family:'楷体 Bold', '楷体';font-weight:700;color:#FFFFFF;">智能学习辅助系统</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u885" class="ax_default image">
        <img id="u885_img" class="img " src="images/首页/u18.png"/>
        <div id="u885_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u886" class="ax_default box_1">
        <div id="u886_div" class=""></div>
        <div id="u886_text" class="text ">
          <p><span>&nbsp;退出登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u887" class="ax_default box_1">
        <div id="u887_div" class=""></div>
        <div id="u887_text" class="text ">
          <p><span>&nbsp; 修改密码</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u888" class="ax_default image">
        <img id="u888_img" class="img " src="images/首页/u21.png"/>
        <div id="u888_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u889" class="ax_default image">
        <img id="u889_img" class="img " src="images/首页/u22.png"/>
        <div id="u889_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u890" class="ax_default image">
        <img id="u890_img" class="img " src="images/首页/u23.png"/>
        <div id="u890_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u867" style="display:none; visibility:hidden;"></div>

      <!-- genderChart (矩形) -->
      <div id="u891" class="ax_default _形状" data-label="genderChart">
        <img id="u891_img" class="img " src="images/员工信息统计/genderchart_u891.svg"/>
        <div id="u891_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u892" class="ax_default _文本">
        <div id="u892_div" class=""></div>
        <div id="u892_text" class="text ">
          <p><span>员工性别统计</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u893" class="ax_default _文本">
        <div id="u893_div" class=""></div>
        <div id="u893_text" class="text ">
          <p><span>员工职位统计</span></p>
        </div>
      </div>

      <!-- jobChart (形状) -->
      <div id="u894" class="ax_default _形状" data-label="jobChart">
        <img id="u894_img" class="img " src="images/员工信息统计/jobchart_u894.svg"/>
        <div id="u894_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 对话框 (动态面板) -->
      <div id="u895" class="ax_default ax_default_hidden" data-label="对话框" style="display:none; visibility: hidden">
        <div id="u895_state0" class="panel_state" data-label="退出确认" style="">
          <div id="u895_state0_content" class="panel_state_content">
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u896" class="ax_default _文本">
        <div id="u896_div" class=""></div>
        <div id="u896_text" class="text ">
          <p style="font-size:28px;text-align:center;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;">员工信息统计-需求说明</span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:28px;text-align:left;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;"><br></span></p><p style="font-size:28px;text-align:left;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;">页面开发规则</span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;">1. 统计企业员工的性别比例 , 以饼状图形式展示 。</span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;">2. 统计企业每一年入职员工的数量，以柱状图展示</span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p><p style="font-size:18px;text-align:left;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;"><br></span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
