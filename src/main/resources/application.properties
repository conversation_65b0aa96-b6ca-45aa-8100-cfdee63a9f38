# =========================== 服务器配置 ===========================
# 服务器端口配置
server.port=8080

# =========================== 数据源配置 ===========================
# 数据库连接URL - 连接到本地MySQL数据库的java_db数据库
spring.datasource.url=***********************************
# MySQL数据库驱动类名
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# 数据库用户名
spring.datasource.username=root
# 数据库密码
spring.datasource.password=123456

# =========================== MyBatis配置 ===========================
# 开启SQL语句日志输出 - 在控制台显示执行的SQL语句，方便调试
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl



# 指定XML映射文件的位置 - 在resources/mappers目录下查找所有xml文件
mybatis.mapper-locations=classpath:mappers/*xml

# 设置实体类的包别名 - 简化XML中的类型引用，不需要写完整包名
mybatis.type-aliases-package=com.ithm.alhz.entity

# 开启驼峰命名自动映射 - 数据库下划线命名自动映射到Java驼峰命名
# 示例: create_time -> createTime, update_time -> updateTime
mybatis.configuration.map-underscore-to-camel-case=true

# =========================== JSON日期格式配置 ===========================
# 配置JSON日期时间格式，支持 "yyyy-MM-dd HH:mm:ss" 格式
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
