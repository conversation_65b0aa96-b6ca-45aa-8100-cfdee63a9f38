<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ithm.alhz.mapper.DeptMapper">

    <!-- 动态更新 - 只更新非空字段 -->
    <update id="update">
        UPDATE dept
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 动态查询 - 根据条件查询部门 -->
    <select id="findByCondition" resultType="com.ithm.alhz.entity.Dept">
        SELECT id, name, create_time, update_time FROM dept
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert">
        INSERT INTO dept (name, create_time, update_time) VALUES
        <foreach collection="list" item="dept" separator=",">
            (#{dept.name}, #{dept.createTime}, #{dept.updateTime})
        </foreach>
    </insert>

    <!-- 根据ID列表查询 -->
    <select id="findByIds" resultType="com.ithm.alhz.entity.Dept">
        SELECT * FROM dept WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

</mapper>