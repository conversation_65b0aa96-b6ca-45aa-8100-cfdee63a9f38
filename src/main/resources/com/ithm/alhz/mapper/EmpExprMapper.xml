<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ithm.alhz.mapper.EmpExprMapper">

    <!-- 批量插入 -->

    <insert id="saveAllEmpExpr">
        insert into emp_expr (emp_id, begin, end, company, job) values
        <foreach collection="exprList" item="empexpr" separator=",">
            (#{empexpr.empId},#{empexpr.begin},#{empexpr.end},#{empexpr.company},#{empexpr.job})
        </foreach>
    </insert>
</mapper>