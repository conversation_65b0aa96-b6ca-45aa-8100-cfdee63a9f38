<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ithm.alhz.mapper.EmpMapper">
    <!-- 新增员工     useGeneratedKeys="true" keyProperty="id"  获取插入的主键值   注解不能对xml产生作用-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into emp (username, password, name, gender, phone, job, salary, image, entry_date, dept_id, create_time,
                         update_time)
        values (#{username}, #{password}, #{name}, #{gender}, #{phone}, #{job}, #{salary}, #{image}, #{entryDate},
                #{deptId}, #{createTime}, #{updateTime}
    </insert>

    <!-- 查询 -->
    <select id="selectAll" resultType="com.ithm.alhz.entity.Emp">
        select e.*, d.name deptName
        from emp e
        left join dept d on e.dept_id = d.id
        <where>
            <if test="name != null and name != ''">
                e.name like concat('%', #{name}, '%')
            </if>
            <if test="gender != null">
                and e.gender = #{gender}
            </if>
            <if test="begin != null">
                and e.entry_date >= #{begin}
            </if>
            <if test="end != null">
                and e.entry_date &lt;= #{end}
            </if>
        </where>
        order by e.update_time desc
    </select>
    <select id="selectEmpByUsername" resultType="java.lang.Long">
        select id from emp where username = #{username};
    </select>
</mapper>