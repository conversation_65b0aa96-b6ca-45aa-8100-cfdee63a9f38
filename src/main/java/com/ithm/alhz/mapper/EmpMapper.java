package com.ithm.alhz.mapper;

import com.ithm.alhz.entity.Emp;
import com.ithm.alhz.entity.EmpExpr;
import com.ithm.alhz.entity.EmpQueryParam;
import org.apache.ibatis.annotations.*;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface EmpMapper {
    @Select("SELECT COUNT(*) FROM EMP")
    Long count();

    /**
     * 分页查询
     * @param start 起始位置（偏移量）
     * @param pageSize 每页显示的记录数
     * @return
     */
    @Select("select e.*, d.name deptName from emp e left join dept d on e.dept_id = d.id limit #{start}, #{pageSize}")
    List<Emp> page(@Param("start") Integer start, @Param("pageSize") Integer pageSize);

    /**
     * 查询所有员工   使用pageheper 不能以 ;结尾 否则拼接存在问题
     * @return
     */
    //@Select("select e.*, d.name deptName from emp e left join dept d on e.dept_id = d.id")
    List<Emp> selectAll(String name, Integer gender, LocalDate begin, LocalDate end);

    List<Emp> selectAll(EmpQueryParam empQueryParam);

    @Delete("delete from emp where id = #{id}")
    void delectById(Integer id);

    //获取主键值   并将主键值给 id  -> 对应表的主键
    /*@Options(useGeneratedKeys = true,keyProperty = "id")
    @Insert("insert into emp (username,password,name,gender,phone,job,salary,image,entry_date,dept_id,create_time,update_time) values (#{username},#{password},#{name},#{gender},#{phone},#{job},#{salary},#{image},#{entryDate},#{deptId},#{createTime},#{updateTime})")*/
    void insert(Emp emp);


}
