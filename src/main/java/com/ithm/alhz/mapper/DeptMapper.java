package com.ithm.alhz.mapper;

import com.ithm.alhz.entity.Dept;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface DeptMapper {

    @Results({
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "update_time", property = "updateTime")
    })
    @Select("select * from dept")
    List<Dept> list();

    @Delete("delete from dept where id =#{id}")
    void delete(Integer id);

    @Insert("insert into dept (name,create_time,update_time) values (#{name},#{createTime},#{updateTime})")
    void insert(Dept dept);

    // # 是占位符 执行时会将#{} 的内容替换成掉  生成预编译sql
    //$ 拼接sql  直接将参数拼接在sql语句中  存在sql注入的问题 不安全性能低
    @Select("select * from dept where id=#{id}")
    Dept getById(Integer id);

    void update(Dept dept);

    // 动态查询方法
    List<Dept> findByCondition(Dept dept);

    // 批量插入方法
    void batchInsert(List<Dept> depts);

    // 根据ID列表查询
    List<Dept> findByIds(@Param("ids") List<Integer> ids);


    @Select("select count(*) from dept")
    Long count();


    @Select("select * from dept limit #{start}, #{pageSize}")
    List<Dept> page(@Param("start") Integer start, @Param("pageSize") Integer pageSize);
}
