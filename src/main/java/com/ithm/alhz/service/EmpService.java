package com.ithm.alhz.service;

import com.ithm.alhz.entity.Emp;
import com.ithm.alhz.entity.EmpQueryParam;
import com.ithm.alhz.entity.PageBean;

import java.time.LocalDate;

public interface EmpService {
    //分页查询
    //PageBean page(String name, Integer gender, LocalDate begin, LocalDate end, Integer page, Integer rows);


    PageBean page(EmpQueryParam empQueryParam);

    void deleteById(Integer id);

    void add(Emp emp);
}
