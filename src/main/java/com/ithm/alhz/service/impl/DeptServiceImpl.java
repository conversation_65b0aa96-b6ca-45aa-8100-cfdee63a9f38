package com.ithm.alhz.service.impl;

import com.ithm.alhz.entity.Dept;
import com.ithm.alhz.entity.PageBean;
import com.ithm.alhz.mapper.DeptMapper;
import com.ithm.alhz.service.DeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class DeptServiceImpl implements DeptService {
    @Autowired
    private DeptMapper deptMapper;


    @Override
    public PageBean page(Integer page, Integer pageSize) {
        //统计总页数
        Long total = deptMapper.count();

        //计算
        Integer start = (page - 1) * pageSize;
        List<Dept> deptList = deptMapper.page(start, pageSize);

        return new PageBean(total,deptList);

    }

    @Override
    public void delete(Integer id) {
        deptMapper.delete(id);
    }

    @Override
    public void insert(Dept dept) {
        deptMapper.insert(dept);
    }

    @Override
    public Dept getById(Integer id) {
        return deptMapper.getById(id);
    }

    @Override
    public void update(Dept dept) {
        deptMapper.update(dept);
    }


}
