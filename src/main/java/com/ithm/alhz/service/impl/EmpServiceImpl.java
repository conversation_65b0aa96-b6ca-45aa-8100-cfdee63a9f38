package com.ithm.alhz.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ithm.alhz.entity.Emp;
import com.ithm.alhz.entity.EmpExpr;
import com.ithm.alhz.entity.EmpQueryParam;
import com.ithm.alhz.entity.PageBean;
import com.ithm.alhz.mapper.EmpExprMapper;
import com.ithm.alhz.mapper.EmpMapper;
import com.ithm.alhz.service.EmpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


@Service
public class EmpServiceImpl implements EmpService {
    @Autowired
    private EmpMapper empMapper;

    @Autowired
    private EmpExprMapper empExprMapper;

    /*@Override
    public PageBean page(Integer page, Integer pageSize) {
        //调用mapper 获取总条数
        Long total = empMapper.count();


        //调用mapper 获取分页列表数据
        Integer start = (page - 1) * pageSize;
        List<Emp> empList = empMapper.page(start, pageSize);

        //封装pageBean 并返回
        return new PageBean(total,empList);
    }*/
    /*@Override
    public PageBean page(String name, Integer gender, LocalDate begin, LocalDate end, Integer page, Integer pageSize) {
        //设置分页参数
        //pagehelper只会对后面的第一次查询进行封装
        PageHelper.startPage(page, pageSize);

        //调用mapper的列表查询方法
        List<Emp> emps = empMapper.selectAll(name,gender,begin,end);
        Page p = (Page) emps;

        List<Emp> empList = empMapper.selectAll(name, gender, begin, end);

        //封装pageBean 并返回
        return new PageBean(p.getTotal(),p.getResult());
    }*/

    @Override
    public PageBean page(EmpQueryParam empQueryParam) {

        //设置分页参数
        //pagehelper只会对后面的第一次查询进行封装
        PageHelper.startPage(empQueryParam.getPage(), empQueryParam.getPageSize());

        //调用mapper的列表查询方法
        List<Emp> emps = empMapper.selectAll(empQueryParam);
        Page p = (Page) emps;

        //封装pageBean 并返回
        return new PageBean(p.getTotal(),p.getResult());
    }

    @Override
    public void deleteById(Integer id) {
        empMapper.delectById(id);
    }




    @Override
    public void add(Emp emp) {
        //初始化密码
        emp.setPassword("123456");

        //初始化添加员工的时间与更新时间
        emp.setCreateTime(LocalDateTime.now());
        emp.setUpdateTime(LocalDateTime.now());

        //1.调用mapper方法 -> 保存员工信息到emp表
        empMapper.insert(emp);

        Integer empId = emp.getId();

        //2.调用mapper方法 -> 保存员工信息到EmpExpr表
        //取出员工工作经历
        List<EmpExpr> exprList = emp.getExprList();

        //补全 exprList的关联信息
        if(!CollectionUtils.isEmpty(exprList)){
            exprList.forEach(x->{
                x.setEmpId(empId);
            });
        }
        //插入员工数据
        empExprMapper.saveAllEmpExpr(exprList);
    }
}
