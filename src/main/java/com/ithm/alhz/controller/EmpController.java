package com.ithm.alhz.controller;


import com.ithm.alhz.entity.Emp;
import com.ithm.alhz.entity.EmpQueryParam;
import com.ithm.alhz.entity.PageBean;
import com.ithm.alhz.entity.Result;
import com.ithm.alhz.service.EmpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@Slf4j
public class EmpController {
    @Autowired
    private EmpService empService;

    /*@GetMapping("/emps")
    //@RequestParam defaultValue 默认值
    public Result page(String name, Integer gender,
                       @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate begin,
                       @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate end,
                       @RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer rows) {
        log.info("分页查询:{},{},{},{},{},{}",name,gender,begin,end, page, rows);

        PageBean pageBean =  empService.page(name,gender,begin,end, page, rows);

        return Result.success(pageBean);
    }*/
    @GetMapping("/emps")
    //@RequestParam defaultValue 默认值
    public Result page(EmpQueryParam empQueryParam) {
        log.info("empQueryParam:{}", empQueryParam);

        PageBean pageBean =  empService.page(empQueryParam);

        return Result.success(pageBean);
    }

    @DeleteMapping("/emps/{id}")
    public Result delete(@PathVariable Integer id)
    {
        log.info("delete:{}", id);
        empService.deleteById(id);
        return  new Result().success();
    }


    /**
     * 新增员工
     * @param emp
     * @return
     */

    @PostMapping("/emps")
    public Result save(@RequestBody Emp emp){
        log.info("新增员工:{}", emp);
        empService.add(emp);
        return Result.success();
    }

}
