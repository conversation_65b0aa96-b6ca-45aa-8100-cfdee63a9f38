package com.ithm.alhz.controller;


import com.ithm.alhz.entity.Dept;
import com.ithm.alhz.entity.PageBean;
import com.ithm.alhz.entity.Result;
import com.ithm.alhz.service.DeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

//等价于@controller + @ResponseBody
@Slf4j
@RestController
public class DeptController {

    @Autowired
    private DeptService deptService;



    @GetMapping("/depts")
    public Result list(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer rows){
        PageBean pageBean = deptService.page(page, rows);

        return Result.success(pageBean);
    }

   /* @GetMapping("/depts")
    public Result list(){
        List<Dept> depts = deptService.page();
        return Result.success(depts);
    }*/

    // 方式一：原始方式获取请求参数
    // @RequestMapping(value = "/depts", method = RequestMethod.DELETE)
    // @DeleteMapping("/depts")
    // public Result delete(HttpServletRequest request){
    //     String id = request.getParameter("id");
    //     int idInt = Integer.parseInt(id);
    //
    //     System.out.println("id="+idInt);
    //     return Result.success();
    // }


    /*//方式二； 通过spring 提供的@RequestParam
    @DeleteMapping("/depts")
    // 将deptid 映射成 id                              required 可以填可以不填 默认为true代表必须传入参数 否则400 bad request
    public Result delete(@RequestParam(value = "id" , required = false) Integer deptid){
        deptService.delete(deptid);
        return Result.success();
    }*/


    //方式三:如果请求参数名与形参变量名相同，直接定义方法形参即可接收 省略@RequestParam
   @DeleteMapping("/depts")
    public Result delete(Integer id) {
       deptService.delete(id);
       return Result.success();
   }

   // RESTful 风格的删除方法（推荐）
   @DeleteMapping("/depts/{id}")
   public Result deleteById(@PathVariable Integer id) {
       deptService.delete(id);
       return Result.success();
   }


    @PostMapping("/depts")
    public Result save(@RequestBody Dept dept ) {
        log.info("save dept: " + dept);

        // 设置创建时间和更新时间为当前时间
        dept.setCreateTime(LocalDateTime.now());
        dept.setUpdateTime(LocalDateTime.now());

        deptService.insert(dept);
        return Result.success();
    }

    //首先我们需要先查询 再修改   分为二步
    @PutMapping("/depts")
    public Result update(@RequestBody Dept dept) {
        log.info("update dept: " + dept);

        // 设置更新时间为当前时间
        dept.setUpdateTime(LocalDateTime.now());

        deptService.update(dept);
        return Result.success();
    }


    //@PathVariable 作用，用于接收路径参数值
    @GetMapping("/depts/{id}")
    public Result getByid(@PathVariable Integer id) {
        Dept dept = deptService.getById(id);
        return Result.success(dept);
    }

}
