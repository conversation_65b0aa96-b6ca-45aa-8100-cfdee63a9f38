package com.ithm.alhz.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一响应结果封装类
 * 用于统一API接口的返回格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result {

    private Integer code; // 响应码：1成功，0失败
    private String msg;   // 响应信息
    private Object data;  // 返回的数据

    // ==================== 成功响应方法 ====================

    /**
     * 成功响应（无数据）
     */
    public static Result success() {
        return new Result(1, "success", null);
    }

    /**
     * 成功响应（带数据）
     */
    public static Result success(Object data) {
        return new Result(1, "success", data);
    }

    /**
     * 成功响应（自定义消息和数据）
     */
    public static Result success(String msg, Object data) {
        return new Result(1, msg, data);
    }

    // ==================== 失败响应方法 ====================

    /**
     * 失败响应（默认消息）
     */
    public static Result error() {
        return new Result(0, "操作失败", null);
    }

    /**
     * 失败响应（自定义消息）
     */
    public static Result error(String msg) {
        return new Result(0, msg, null);
    }

    /**
     * 失败响应（自定义状态码和消息）
     */
    public static Result error(Integer code, String msg) {
        return new Result(code, msg, null);
    }
}
