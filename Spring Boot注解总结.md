# Spring Boot 注解完整总结文档

## 目录
1. [HTTP请求映射注解](#1-http请求映射注解)
2. [参数绑定注解](#2-参数绑定注解)
3. [响应处理注解](#3-响应处理注解)
4. [验证注解](#4-验证注解)
5. [异常处理注解](#5-异常处理注解)
6. [跨域注解](#6-跨域注解)
7. [依赖注入注解](#7-依赖注入注解)
8. [组件注解](#8-组件注解)
9. [条件注解](#9-条件注解)
10. [缓存注解](#10-缓存注解)
11. [事务注解](#11-事务注解)
12. [定时任务注解](#12-定时任务注解)
13. [配置相关注解](#13-配置相关注解)
14. [实际应用示例](#14-实际应用示例)

---

## 1. HTTP请求映射注解

### 基础映射注解
| 注解 | 作用 | HTTP方法 | 示例 |
|------|------|----------|------|
| `@RequestMapping` | 通用请求映射 | 可指定任意方法 | `@RequestMapping(value="/users", method=RequestMethod.GET)` |
| `@GetMapping` | GET请求映射 | GET | `@GetMapping("/users")` |
| `@PostMapping` | POST请求映射 | POST | `@PostMapping("/users")` |
| `@PutMapping` | PUT请求映射 | PUT | `@PutMapping("/users/{id}")` |
| `@DeleteMapping` | DELETE请求映射 | DELETE | `@DeleteMapping("/users/{id}")` |
| `@PatchMapping` | PATCH请求映射 | PATCH | `@PatchMapping("/users/{id}")` |

### 使用示例
```java
@RestController
@RequestMapping("/api/v1")
public class UserController {
    
    @GetMapping("/users")           // GET /api/v1/users
    public List<User> getUsers() { }
    
    @PostMapping("/users")          // POST /api/v1/users
    public Result createUser(@RequestBody User user) { }
    
    @PutMapping("/users/{id}")      // PUT /api/v1/users/123
    public Result updateUser(@PathVariable Integer id, @RequestBody User user) { }
    
    @DeleteMapping("/users/{id}")   // DELETE /api/v1/users/123
    public Result deleteUser(@PathVariable Integer id) { }
}
```

---

## 2. 参数绑定注解

### 路径参数
| 注解 | 作用 | 示例 |
|------|------|------|
| `@PathVariable` | 获取URL路径中的参数 | `@GetMapping("/users/{id}")` |

```java
@GetMapping("/users/{id}")
public User getUser(@PathVariable Integer id) { }

@GetMapping("/users/{userId}/posts/{postId}")
public Post getPost(@PathVariable Integer userId, @PathVariable Integer postId) { }

// 参数名不同时指定名称
@GetMapping("/users/{id}")
public User getUser(@PathVariable("id") Integer userId) { }
```

### 请求参数
| 注解 | 作用 | 示例 |
|------|------|------|
| `@RequestParam` | 获取请求参数（查询参数、表单参数） | `@RequestParam String name` |

```java
// GET /users?name=张三&age=25
@GetMapping("/users")
public List<User> getUsers(@RequestParam String name, @RequestParam Integer age) { }

// 可选参数
@GetMapping("/users")
public List<User> getUsers(@RequestParam(required = false) String name) { }

// 默认值
@GetMapping("/users")
public List<User> getUsers(@RequestParam(defaultValue = "1") Integer page) { }

// 参数名映射
@GetMapping("/users")
public List<User> getUsers(@RequestParam("userName") String name) { }
```

### 请求体参数
| 注解 | 作用 | 示例 |
|------|------|------|
| `@RequestBody` | 获取请求体中的JSON/XML数据 | `@PostMapping("/users")` |

```java
@PostMapping("/users")
public Result createUser(@RequestBody User user) { }

@PutMapping("/users/{id}")
public Result updateUser(@PathVariable Integer id, @RequestBody User user) { }
```

### 其他参数注解
| 注解 | 作用 | 示例 |
|------|------|------|
| `@RequestHeader` | 获取请求头信息 | `@RequestHeader("Authorization") String token` |
| `@CookieValue` | 获取Cookie值 | `@CookieValue("sessionId") String sessionId` |

```java
@GetMapping("/users")
public List<User> getUsers(@RequestHeader("Authorization") String token,
                          @CookieValue("sessionId") String sessionId) { }
```

---

## 3. 响应处理注解

| 注解 | 作用 | 使用场景 |
|------|------|----------|
| `@ResponseBody` | 将返回值直接写入HTTP响应体 | 返回JSON数据 |
| `@RestController` | 等价于 @Controller + @ResponseBody | REST API控制器 |
| `@Controller` | 标识控制器类 | 传统MVC控制器 |

```java
@RestController  // 所有方法都返回JSON
public class ApiController {
    @GetMapping("/users")
    public List<User> getUsers() {
        return userService.findAll(); // 自动转换为JSON
    }
}

@Controller      // 返回视图名称
public class PageController {
    @GetMapping("/login")
    public String loginPage() {
        return "login"; // 返回login.html页面
    }
    
    @GetMapping("/api/users")
    @ResponseBody    // 这个方法返回JSON
    public List<User> getUsers() {
        return userService.findAll();
    }
}
```

---

## 4. 验证注解

| 注解 | 作用 | 示例 |
|------|------|------|
| `@Valid` | 启用参数验证 | `@Valid @RequestBody User user` |
| `@Validated` | Spring的验证注解 | `@Validated @RequestBody User user` |

### Bean Validation注解
| 注解 | 作用 | 示例 |
|------|------|------|
| `@NotNull` | 不能为null | `@NotNull(message = "姓名不能为空")` |
| `@NotEmpty` | 不能为空（字符串、集合） | `@NotEmpty private String name;` |
| `@NotBlank` | 不能为空白（字符串） | `@NotBlank private String name;` |
| `@Min` | 最小值 | `@Min(value = 18, message = "年龄不能小于18")` |
| `@Max` | 最大值 | `@Max(value = 100)` |
| `@Email` | 邮箱格式 | `@Email(message = "邮箱格式不正确")` |
| `@Pattern` | 正则表达式 | `@Pattern(regexp = "^1[3-9]\\d{9}$")` |

```java
@PostMapping("/users")
public Result createUser(@Valid @RequestBody User user) {
    // 如果user对象验证失败，会自动返回400错误
}

// User实体类中
public class User {
    @NotNull(message = "姓名不能为空")
    @NotBlank(message = "姓名不能为空白")
    private String name;
    
    @Min(value = 18, message = "年龄不能小于18")
    @Max(value = 100, message = "年龄不能大于100")
    private Integer age;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
}
```

---

## 5. 异常处理注解

| 注解 | 作用 | 使用场景 |
|------|------|----------|
| `@ExceptionHandler` | 处理特定异常 | 方法级异常处理 |
| `@ControllerAdvice` | 全局异常处理 | 返回视图的全局异常处理 |
| `@RestControllerAdvice` | 全局异常处理（返回JSON） | REST API的全局异常处理 |

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(Exception.class)
    public Result handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error("系统异常：" + e.getMessage());
    }
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result handleValidException(MethodArgumentNotValidException e) {
        return Result.error("参数验证失败");
    }
    
    @ExceptionHandler(DataIntegrityViolationException.class)
    public Result handleDataException(DataIntegrityViolationException e) {
        return Result.error("数据操作失败");
    }
}
```

---

## 6. 跨域注解

| 注解 | 作用 | 示例 |
|------|------|------|
| `@CrossOrigin` | 处理跨域请求 | `@CrossOrigin(origins = "*")` |

```java
@RestController
@CrossOrigin(origins = "http://localhost:3000") // 允许特定域名
public class UserController {

    @CrossOrigin(origins = "*") // 允许所有域名
    @GetMapping("/users")
    public List<User> getUsers() { }

    @CrossOrigin(origins = {"http://localhost:3000", "http://localhost:8080"})
    @PostMapping("/users")
    public Result createUser(@RequestBody User user) { }
}
```

---

## 7. 依赖注入注解

| 注解 | 作用 | 示例 |
|------|------|------|
| `@Autowired` | 自动装配（按类型） | `@Autowired private UserService userService;` |
| `@Resource` | 按名称注入 | `@Resource(name = "userService")` |
| `@Qualifier` | 指定注入的Bean名称 | `@Qualifier("redisUserService")` |
| `@Value` | 注入配置值 | `@Value("${app.name}")` |

```java
@RestController
public class UserController {

    @Autowired
    private UserService userService;

    @Value("${app.name}")
    private String appName;

    @Value("${server.port:8080}")  // 默认值8080
    private Integer serverPort;

    @Autowired
    @Qualifier("redisUserService")
    private UserService redisUserService;

    @Resource(name = "cacheUserService")
    private UserService cacheUserService;
}
```

---

## 8. 组件注解

| 注解 | 作用 | 层级 | 示例 |
|------|------|------|------|
| `@Component` | 通用组件 | 通用 | `@Component public class Utils` |
| `@Service` | 业务层组件 | Service层 | `@Service public class UserService` |
| `@Repository` | 数据访问层组件 | DAO层 | `@Repository public class UserDao` |
| `@Controller` | 控制层组件 | Controller层 | `@Controller public class UserController` |
| `@RestController` | REST控制层组件 | Controller层 | `@RestController public class ApiController` |
| `@Configuration` | 配置类 | 配置 | `@Configuration public class AppConfig` |

```java
@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;
}

@Repository
public class UserRepository {
    // 数据访问逻辑
}

@Configuration
public class AppConfig {
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
```

---

## 9. 条件注解

| 注解 | 作用 | 示例 |
|------|------|------|
| `@ConditionalOnProperty` | 根据配置属性条件加载 | `@ConditionalOnProperty(name="app.cache.enabled", havingValue="true")` |
| `@ConditionalOnClass` | 根据类存在条件加载 | `@ConditionalOnClass(RedisTemplate.class)` |
| `@ConditionalOnMissingBean` | 当Bean不存在时加载 | `@ConditionalOnMissingBean(DataSource.class)` |
| `@Profile` | 根据环境profile加载 | `@Profile("dev")` |

```java
@Configuration
public class CacheConfig {

    @Bean
    @ConditionalOnProperty(name = "app.cache.enabled", havingValue = "true")
    public CacheManager cacheManager() {
        return new RedisCacheManager();
    }

    @Bean
    @ConditionalOnClass(RedisTemplate.class)
    public RedisTemplate<String, Object> redisTemplate() {
        return new RedisTemplate<>();
    }

    @Bean
    @Profile("dev")
    public DataSource devDataSource() {
        return new H2DataSource();
    }
}
```

---

## 10. 缓存注解

| 注解 | 作用 | 示例 |
|------|------|------|
| `@Cacheable` | 缓存方法结果 | `@Cacheable("users")` |
| `@CacheEvict` | 清除缓存 | `@CacheEvict("users")` |
| `@CachePut` | 更新缓存 | `@CachePut("users")` |
| `@EnableCaching` | 启用缓存功能 | `@EnableCaching` |

```java
@Service
public class UserService {

    @Cacheable(value = "users", key = "#id")
    public User findById(Integer id) {
        return userRepository.findById(id);
    }

    @CachePut(value = "users", key = "#user.id")
    public User update(User user) {
        return userRepository.save(user);
    }

    @CacheEvict(value = "users", key = "#id")
    public void delete(Integer id) {
        userRepository.deleteById(id);
    }

    @CacheEvict(value = "users", allEntries = true)
    public void clearAllCache() {
        // 清除所有用户缓存
    }
}

@SpringBootApplication
@EnableCaching
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```
